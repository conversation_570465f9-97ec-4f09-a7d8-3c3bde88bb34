<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="account_tour_upload_bill" model="ir.ui.view">
            <field name="name">account.tour.upload.bill</field>
            <field name="model">account.tour.upload.bill</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <h2>With Odoo, you won't have to record bills manually</h2>
                        <p>We process bills automatically so that you only have to validate them. Choose how you want to test our artificial intelligence engine:</p>
                        <div class="grid">
                            <field name="selection" widget="radio" class="g-col-4"/>
                            <field name="preview_invoice" widget="html" invisible="selection != 'sample'" class="g-col-8"/>
                            <field name="attachment_ids" widget="many2many_binary" string="Attach a file" invisible="selection != 'upload'" class="g-col-8"/>
                        </div>
                    </sheet>
                    <footer>
                        <button string="Continue" type="object" name="apply" class="btn-primary" data-hotkey="q"/>
                        <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="x" />
                    </footer>
               </form>
            </field>
        </record>

        <record id="account_tour_upload_bill_email_confirm" model="ir.ui.view">
            <field name="name">account.tour.upload.bill.email.confirm</field>
            <field name="model">account.tour.upload.bill.email.confirm</field>
            <field name="arch" type="xml">
                <form>
                    <field name="email_alias" invisible="1"/>
                    <sheet>
                        <p invisible="not email_alias">Send your email to <field name="email_alias" class="oe_inline" widget="email"/> with a pdf of an invoice as attachment.</p>
                        <p invisible="not email_alias">Once done, press continue.</p>
                        <p invisible="email_alias">Configure your email server first and send your bill by email.</p>
                        <a type='action' name='%(action_open_settings)d' class="btn btn-link" role="button" invisible="email_alias"><i class="oi oi-fw o_button_icon oi-arrow-right"/> Configure Email Servers</a>
                    </sheet>
                    <footer>
                        <button string="Continue" type="object" name="apply" class="btn-primary" data-hotkey="q" invisible="not email_alias"/>
                        <button string="Discard" class="btn-secondary" special="cancel" data-hotkey="x" invisible="not email_alias"/>
                        <button string="Close" class="btn-secondary" special="cancel" data-hotkey="x" invisible="email_alias"/>
                    </footer>
               </form>
            </field>
        </record>
    </data>
</odoo>
