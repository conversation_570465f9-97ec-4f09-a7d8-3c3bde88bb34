<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="account_move_send_form" model="ir.ui.view">
        <field name="name">account.move.send.form</field>
        <field name="model">account.move.send</field>
        <field name="groups_id" eval="[Command.link(ref('base.group_user'))]"/>
        <field name="arch" type="xml">
            <form>
                <!-- Invisible fields -->
                <field name="company_id" invisible="1"/>
                <field name="move_ids" invisible="1"/>
                <field name="mode" invisible="1"/>
                <field name="enable_download" invisible="1"/>
                <field name="enable_send_mail" invisible="1"/>
                <field name="send_mail_readonly" invisible="1"/>
                <field name="send_mail_warning_message" invisible="1"/>
                <field name="display_mail_composer" invisible="1"/>
                <field name="mail_lang" invisible="1"/>

                <div class="m-0" name="warnings">
                    <field name="send_mail_warning_message" class="o_field_html" widget="actionable_errors"/>
                </div>

                <!-- Options -->
                <div name="options" class="row">
                    <div name="standard_options" class="col-3">
                        <div name="option_download"
                            invisible="not enable_download">
                            <field name="checkbox_download"/>
                            <b><label for="checkbox_download"/></b>
                        </div>
                        <div name="option_send_mail"
                            invisible="not enable_send_mail">
                            <field name="checkbox_send_mail"
                                readonly="send_mail_readonly"/>
                            <b><label for="checkbox_send_mail"/></b>
                            <i class="fa fa-question-circle ml4"
                                role="img"
                                aria-label="Warning"
                                title="The email address is unknown on the partner"
                                invisible="not send_mail_readonly"/>
                        </div>
                    </div>
                    <div name="advanced_options" class="col-3"/>
                </div>

                <!-- Mail -->
                <div invisible="not checkbox_send_mail">
                    <group invisible="not display_mail_composer">
                        <label for="mail_partner_ids" string="Recipients"/>
                        <div>
                            <span>Followers of the document and</span>
                            <field name="mail_partner_ids"
                                   widget="many2many_tags_email"
                                   placeholder="Add contacts to notify..."
                                   options="{'no_quick_create': True}"
                                   context="{'show_email': True, 'form_view_ref': 'base.view_partner_simple_form'}"/>
                        </div>
                        <field name="mail_subject"
                               placeholder="Subject..."
                               required="checkbox_send_mail and mode == 'invoice_single'"/>
                    </group>
                    <field name="mail_body"
                           class="oe-bordered-editor"
                           options="{'style-inline': true}"
                           invisible="not display_mail_composer"/>
                    <group>
                        <group invisible="not display_mail_composer">
                            <field name="mail_attachments_widget"
                                   widget="mail_attachments"
                                   string="Attach a file"
                                   nolabel="1"
                                   colspan="2"/>
                        </group>
                        <group>
                            <field name="mail_template_id"
                                   required="mode == 'invoice_multi'"
                                   options="{'no_create': True, 'no_edit': True}"
                                   context="{'default_model': 'account.move'}"/>
                        </group>
                    </group>
                </div>

                <footer>
                    <button string="Send &amp; Print"
                            data-hotkey="q"
                            name="action_send_and_print"
                            type="object"
                            class="print btn-primary o_mail_send">
                    </button>
                    <button string="Cancel"
                            data-hotkey="x"
                            special="cancel"
                            class="btn-secondary"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="invoice_send" model="ir.actions.server">
        <field name="name">Send &amp; Print</field>
        <field name="state">code</field>
        <field name="model_id" ref="model_account_move"/>
        <field name="binding_model_id" ref="model_account_move"/>
        <field name="binding_view_types">list</field>
        <field name="code">
            if records:
                action = records.action_send_and_print()
        </field>
    </record>

</odoo>
