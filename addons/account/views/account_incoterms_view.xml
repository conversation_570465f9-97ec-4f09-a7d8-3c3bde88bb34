<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- account incoterms -->
        <record id="view_incoterms_tree" model="ir.ui.view">
            <field name="name">account.incoterms.tree</field>
            <field name="model">account.incoterms</field>
            <field name="arch" type="xml">
                <tree string="Incoterms" editable="bottom">
                    <field name="active" column_invisible="True"/>
                    <field name="code"/>
                    <field colspan="4" name="name"/>
                </tree>
            </field>
        </record>

        <record id="account_incoterms_form" model="ir.ui.view">
            <field name="name">account.incoterms.form</field>
            <field name="model">account.incoterms</field>
            <field name="arch" type="xml">
                <form string="Incoterms">
                    <sheet>
                        <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="name"/>
                            <field name="code"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="account_incoterms_view_search" model="ir.ui.view">
            <field name="name">account.incoterms.search</field>
            <field name="model">account.incoterms</field>
            <field name="arch" type="xml">
                <search string="Incoterms">
                    <field name="name"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                </search>
            </field>
        </record>

        <record id="action_incoterms_tree" model="ir.actions.act_window">
            <field name="name">Incoterms</field>
            <field name="res_model">account.incoterms</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new incoterm
              </p><p>
                Incoterms are used to divide transaction costs and responsibilities between buyer and seller.
              </p>
            </field>
        </record>

    </data>
</odoo>
