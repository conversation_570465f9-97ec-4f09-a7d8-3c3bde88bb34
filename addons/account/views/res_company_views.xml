<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_company_form" model="ir.ui.view">
        <field name="name">res.company.form.inherit.account</field>
        <field name="model">res.company</field>
        <field name="inherit_id" ref="base.view_company_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='country_id']" position="after">
                <field name="country_code" invisible="1"/>
                <field name="account_enabled_tax_country_ids" invisible="1"/>
            </xpath>
            <xpath expr="//sheet" position="after">
                <div class="oe_chatter">
                    <field name="message_follower_ids" groups="base.group_user"/>
                    <field name="message_ids"/>
                </div>
            </xpath>
        </field>
    </record>

    <record id="res_company_view_form_terms" model="ir.ui.view">
        <field name="name">res.company.view.form.terms</field>
        <field name="model">res.company</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <form>
                <field name="invoice_terms_html" class="oe_account_terms" nolabel="1"/>
                <footer>
                    <button string="Save" special="save" class="btn-primary"/>
                    <button string="Discard" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Onboarding -->
    <record id="res_company_form_view_onboarding" model="ir.ui.view">
        <field name="name">res.company.form.view.onboarding</field>
        <field name="model">res.company</field>
        <field name="inherit_id" ref="base.view_company_form"/>
        <field name="mode">primary</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='social_media']" position="replace"/>
            <form position="attributes">
                <attribute name="js_class">company_onboarding_form</attribute>
            </form>
        </field>
    </record>

    <record id="res_company_form_view_onboarding_sale_tax" model="ir.ui.view">
        <field name="name">res.company.form.view.onboarding.sale.tax</field>
        <field name="model">res.company</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <form>
                <div class="mb16">Choose a default sales tax for your products.</div>
                <label for="account_sale_tax_id" string="Sales Tax"/>
                <field name="account_sale_tax_id" />
                <footer>
                    <button string="Apply" class="btn btn-primary" type="object" name="action_save_onboarding_sale_tax" data-hotkey="q" />
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="x" />
                </footer>
            </form>
        </field>
    </record>
</odoo>
