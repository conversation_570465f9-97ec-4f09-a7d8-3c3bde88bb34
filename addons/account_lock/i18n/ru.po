# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# <PERSON>, 2023
# Wil <PERSON>doo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"Any new All Users Lock Date must be posterior (or equal) to the previous "
"one."
msgstr ""
"Любая новая дата блокировки всех пользователей должна быть позже (или равна)"
" предыдущей."

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for accountants is irreversible and can't be removed."
msgstr "Дата блокировки для бухгалтеров необратима и не может быть снята."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr ""
"Новая дата блокировки для налога должна быть установлена после даты "
"предыдущей блокировки."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr "Дата блокировки налогов необратима и не может быть удалена."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the All Users "
"Lock Date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Нельзя заблокировать период, который еще не закончился. Поэтому дата "
"блокировки для всех пользователей должна быть раньше (или равна) последнего "
"дня предыдущего месяца."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the tax lock "
"date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Вы не можете заблокировать период, который еще не закончился. Поэтому дата "
"блокировки налога должна быть предшествующей (или равной) последнему дню "
"предыдущего месяца."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot set stricter restrictions on accountants than on users. "
"Therefore, the All Users Lock Date must be anterior (or equal) to the "
"Invoice/Bills Lock Date."
msgstr ""
"Для бухгалтеров нельзя устанавливать более строгие ограничения, чем для "
"пользователей. Поэтому дата блокировки всех пользователей должна быть раньше"
" (или равна) даты блокировки счетов/фактур."
