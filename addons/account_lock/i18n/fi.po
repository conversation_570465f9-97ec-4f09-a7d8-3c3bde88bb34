# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# <PERSON><PERSON> <kari.lind<PERSON>@emsystems.fi>, 2023
# Konsta Aavaranta, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Konsta Aavaranta, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"Any new All Users Lock Date must be posterior (or equal) to the previous "
"one."
msgstr ""
"Uusi kaikkien käyttäjien lukituspäivämäärä on oltava edellistä myöhäisempi "
"(tai yhtä suuri)."

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "Yritykset"

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for accountants is irreversible and can't be removed."
msgstr ""
"Lukituspäivämäärä kirjanpitäjille on peruuttamaton, eikä sitä voi poistaa."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr ""
"Uusi veroraportin lukituspäivämäärä on asetettava edellisen lukituspäivän "
"jälkeen."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr ""
"Veroraportin lukituspäivämäärä on peruuttamaton, eikä sitä voi poistaa."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the All Users "
"Lock Date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Et voi lukita ajanjaksoa, joka ei ole vielä päättynyt. Siksi kaikkien "
"käyttäjien lukituspäivämäärän on oltava edellisen kuukauden viimeistä päivää"
" edeltävä päivä (tai yhtä suuri)."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the tax lock "
"date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Et voi lukita ajanjaksoa, joka ei ole vielä päättynyt. Siksi veroraportin "
"lukituspäivämäärä on oltava edellisen kuukauden viimeistä päivää edeltävä "
"päivä (tai yhtä suuri)."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot set stricter restrictions on accountants than on users. "
"Therefore, the All Users Lock Date must be anterior (or equal) to the "
"Invoice/Bills Lock Date."
msgstr ""
"Et voi asettaa tiukempia rajoituksia kirjanpitäjille kuin käyttäjille. Siksi"
" kaikkien käyttäjien lukituspäivämäärän on oltava laskujen "
"lukituspäivämäärää edeltävä päivä (tai yhtä suuri)."
