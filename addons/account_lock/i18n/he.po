# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# yael terner, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: yael terner, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"Any new All Users Lock Date must be posterior (or equal) to the previous "
"one."
msgstr ""

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for accountants is irreversible and can't be removed."
msgstr ""

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr "תאריך נעילת המס הבא צריך להיות אחרי תאריך נעילת המס הקודם."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr "תאריך נעילת המס הוא בלתי הפיך ואי אפשר לשנותו."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the All Users "
"Lock Date must be anterior (or equal) to the last day of the previous month."
msgstr ""

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the tax lock "
"date must be anterior (or equal) to the last day of the previous month."
msgstr ""

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot set stricter restrictions on accountants than on users. "
"Therefore, the All Users Lock Date must be anterior (or equal) to the "
"Invoice/Bills Lock Date."
msgstr "הגבל למשתמש"
