# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"Any new All Users Lock Date must be posterior (or equal) to the previous "
"one."
msgstr ""
"يجب أن تكون تواريخ الإقفال الجديدة لكافة المستخدمين بعد (أو مكافئة) لما "
"قبلها. "

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for accountants is irreversible and can't be removed."
msgstr "لا يمكن عكس تاريخ الإقفال للمحاسبين ولا يمكن إزالته. "

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr ""
"يجب أن يتم تعيين تاريخ إقفال الضريبة الجديد بعد تاريخ إقفال الضريبة السابق. "

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr "لا يمكن عكس أو إزالة تاريخ إقفال الضريبة "

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the All Users "
"Lock Date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"لا يمكنك إقفال فترة لم تنته بعد، ولذلك، يجب أن يكون تاريخ إقفال كافة "
"المستخدِمين سابقاً (أو مكافئاً) لتاريخ آخر يوم من الشهر الماضي. "

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the tax lock "
"date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"لا يمكنك إقفال فترة لم تنته بعد، ولذلك، يجب أن يكون تاريخ إقفال الضريبة "
"سابقاً (أو مكافئاً) لتاريخ آخر يوم من الشهر الماضي. "

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot set stricter restrictions on accountants than on users. "
"Therefore, the All Users Lock Date must be anterior (or equal) to the "
"Invoice/Bills Lock Date."
msgstr ""
"لا يمكن أن تكون التقييدات التي تضعها على المحاسبين أكثر شدة من المستخدمين، "
"ولذلك، يجب أن يكون تاريخ إقفال كافة المستخدِمين سابقاً (أو مكافئاً) لتاريخ "
"آخر يوم من الشهر الماضي. "
