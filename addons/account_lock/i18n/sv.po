# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# Simon S, 2023
# Lasse L, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Lasse L, 2023\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"Any new All Users Lock Date must be posterior (or equal) to the previous "
"one."
msgstr ""
"Alla nya låsdatum för alla användare måste vara efterföljande (eller lika "
"med) det föregående."

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "Bolag"

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for accountants is irreversible and can't be removed."
msgstr "Låsdatumet för bokförare är oåterkalleligt och kan inte tas bort."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr ""
"Det nya skattelåsdatumet måste ställas in efter det föregående låsdatumet."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr "Skattelåsdatumet är oåterkalleligt och kan inte tas bort."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the All Users "
"Lock Date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Du kan inte låsa en period som ännu inte har avslutats. Därför måste alla "
"användares låsdatum vara tidigare (eller lika med) den sista dagen i "
"föregående månad."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the tax lock "
"date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"Du kan inte låsa en period som ännu inte har avslutats. Därför måste "
"skattelåsdatumet vara tidigare (eller lika med) den sista dagen i föregående"
" månad."

#. module: account_lock
#. odoo-python
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot set stricter restrictions on accountants than on users. "
"Therefore, the All Users Lock Date must be anterior (or equal) to the "
"Invoice/Bills Lock Date."
msgstr ""
"Du kan inte ställa strängare begränsningar på bokförare än på användare. "
"Därför måste alla användares låsdatum vara tidigare (eller lika med) "
"faktura/räkningar låsdatum."
