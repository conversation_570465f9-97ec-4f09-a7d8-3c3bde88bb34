# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_address_extended
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <ossi.manty<PERSON><PERSON>@obs-solutions.fi>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_res_country_city_extended_form
msgid "<span class=\"o_stat_text\">Cities</span>"
msgstr "<span class=\"o_stat_text\">Kaupungit</span>"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "<span> - </span>"
msgstr "<span> - </span>"

#. module: base_address_extended
#: model:ir.model.fields,help:base_address_extended.field_res_country__enforce_cities
#: model:ir.model.fields,help:base_address_extended.field_res_partner__country_enforce_cities
#: model:ir.model.fields,help:base_address_extended.field_res_users__country_enforce_cities
msgid ""
"Check this box to ensure every address created in that country has a 'City' "
"chosen in the list of the country's cities."
msgstr ""
"Valitse tämä varmistaaksesi, että jokainen osoite sisältää kaupungin, joka "
"on tämän maan kaupunkien luettelossa."

#. module: base_address_extended
#: model:ir.actions.act_window,name:base_address_extended.action_res_city_tree
#: model:ir.ui.menu,name:base_address_extended.menu_res_city
msgid "Cities"
msgstr "Kaupungit"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_city
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_city_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_filter
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_tree
msgid "City"
msgstr "Kaupunki"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__city_id
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__city_id
msgid "City ID"
msgstr "Kaupunki ID"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_partner
msgid "Contact"
msgstr "Kontakti"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_country
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__country_id
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Country"
msgstr "Maa"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__create_date
msgid "Created on"
msgstr "Luotu"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: base_address_extended
#: model_terms:ir.actions.act_window,help:base_address_extended.action_res_city_tree
msgid ""
"Display and manage the list of all cities that can be assigned to\n"
"                your partner records. Note that an option can be set on each country separately\n"
"                to enforce any address of it to have a city in this list."
msgstr ""
"Näytä ja hallitse partneritietueisiin liitettäviä kaupunkeja.\n"
"                Huomaa, että kunkin maan osalta voidaan valita erikseen pakollinen\n"
"                tarkistus siitä, että kyseisessä maassa on listalla oleva kaupunki."

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number2
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number2
msgid "Door"
msgstr "Ovi"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Door #"
msgstr "Ovi #"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_country__enforce_cities
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__country_enforce_cities
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__country_enforce_cities
msgid "Enforce Cities"
msgstr "Pakota kaupunkien tarkistus"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number
msgid "House"
msgstr "Talo"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "House #"
msgstr "Talo #"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__id
msgid "ID"
msgstr "ID"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__name
msgid "Name"
msgstr "Nimi"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_filter
msgid "Search City"
msgstr "Etsi kaupunkia"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__state_id
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "State"
msgstr "Alue"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street"
msgstr "Katuosoite"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street 2..."
msgstr "Katuosoite 2..."

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_name
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_name
msgid "Street Name"
msgstr "Kadun nimi"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street..."
msgstr "Katu..."

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "ZIP"
msgstr "Postinumero"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__zipcode
msgid "Zip"
msgstr "Postinumero"
