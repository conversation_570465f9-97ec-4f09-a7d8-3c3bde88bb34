# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_address_extended
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON>. <<EMAIL>>, 2023
# UAB "Draugiš<PERSON> sprendimai" <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>Lau<PERSON> <<EMAIL>>, 2023
# <PERSON>as V<PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# Silvi<PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_res_country_city_extended_form
msgid "<span class=\"o_stat_text\">Cities</span>"
msgstr ""

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "<span> - </span>"
msgstr ""

#. module: base_address_extended
#: model:ir.model.fields,help:base_address_extended.field_res_country__enforce_cities
#: model:ir.model.fields,help:base_address_extended.field_res_partner__country_enforce_cities
#: model:ir.model.fields,help:base_address_extended.field_res_users__country_enforce_cities
msgid ""
"Check this box to ensure every address created in that country has a 'City' "
"chosen in the list of the country's cities."
msgstr ""
"Pažymėkite šį laukelį, kad užtikrintumėte, jog kiekvienas tos šalies adresas"
" turėtų pasirinktą miestą iš šalies miestų sąrašo."

#. module: base_address_extended
#: model:ir.actions.act_window,name:base_address_extended.action_res_city_tree
#: model:ir.ui.menu,name:base_address_extended.menu_res_city
msgid "Cities"
msgstr "Miestai"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_city
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_city_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_filter
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_tree
msgid "City"
msgstr "Miestas"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__city_id
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__city_id
msgid "City ID"
msgstr ""

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_partner
msgid "Contact"
msgstr "Kontaktas"

#. module: base_address_extended
#: model:ir.model,name:base_address_extended.model_res_country
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__country_id
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Country"
msgstr "Valstybė"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: base_address_extended
#: model_terms:ir.actions.act_window,help:base_address_extended.action_res_city_tree
msgid ""
"Display and manage the list of all cities that can be assigned to\n"
"                your partner records. Note that an option can be set on each country separately\n"
"                to enforce any address of it to have a city in this list."
msgstr ""
"Rodyti ir valdyti visų miestų, kurie gali būti priskirti jūsų\n"
"partnerių įrašams, sąrašą. Atkreipkite dėmesį, kad šis pasirinkimas gali būti nustatytas kiekvienai\n"
"šaliai atskirai, kad būtų forsuojamas privalomas miesto iš šio sąrašo turėjimas."

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number2
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number2
msgid "Door"
msgstr "Butas"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Door #"
msgstr ""

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_country__enforce_cities
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__country_enforce_cities
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__country_enforce_cities
msgid "Enforce Cities"
msgstr "Forsuoti miestus"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_number
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_number
msgid "House"
msgstr "Namas"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "House #"
msgstr ""

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__id
msgid "ID"
msgstr "ID"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__name
msgid "Name"
msgstr "Vardas, Pavardė"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.view_city_filter
msgid "Search City"
msgstr "Ieškoti miesto"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__state_id
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "State"
msgstr "Būsena"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street"
msgstr "Gatvė"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street 2..."
msgstr "Gatvė 2..."

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_partner__street_name
#: model:ir.model.fields,field_description:base_address_extended.field_res_users__street_name
msgid "Street Name"
msgstr "Gatvės pavadinimas"

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "Street..."
msgstr "Gatvė..."

#. module: base_address_extended
#: model_terms:ir.ui.view,arch_db:base_address_extended.address_street_extended_form
msgid "ZIP"
msgstr "Pašto kodas"

#. module: base_address_extended
#: model:ir.model.fields,field_description:base_address_extended.field_res_city__zipcode
msgid "Zip"
msgstr "Pašto kodas"
