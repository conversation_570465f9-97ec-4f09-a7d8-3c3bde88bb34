# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# <PERSON>, 2023
# <PERSON> <mbo<PERSON><EMAIL>>, 2023
# コフスタジオ, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: コフスタジオ, 2024\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage of Price: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage of Price Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"- Grupa poreza: Porez je skup podporeza.\n"
"- Fiksni: Iznos poreza ostaje isti bez obzira na cenu.\n"
"- Procenat od cene: Iznos poreza je % od cene:\n"
"    npr. 100 * (1 + 10%) = 110 (bez uključene cene)\n"
"    npr. 110 / (1 + 10%) = 100 (uključena cena)\n"
"- Procenat od cene sa uključenim porezom: Iznos poreza je deljenje cene:\n"
"    npr. 180 / (1 - 10%) = 200 (bez uključene cene)\n"
"    npr. 200 * (1 - 10%) = 180 (uključena cena)"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_applicable
msgid "Applicable Code"
msgstr "Primenjivi kod"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Izračunajte iznos poreza postavljanjem promenljive 'result'.\n"
"\n"
":param base_amount: float, stvarni iznos na koji se primenjuje porez\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company zapis jedinstvenog skupa podataka\n"
":param product: proizvod.proizvod zapis jedinstvenog skupa podataka ili None\n"
":param partner: res.partner zapis jedinstvenog skupa podataka ili None"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Odredite da li će se porez primenjivati postavljanjem promenljive 'result' na True ili False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company zapis jedinstvenog skupa podataka\n"
":param product: product.product zapis jedinstvenog skupa podataka ili None\n"
":param partner: res.partner zapis jedinstvenog skupa podataka ili None"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_compute
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
msgid "Python Code"
msgstr "Python kod"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Porez"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
msgid "Tax Computation"
msgstr "Izračunavanje poreza"

#. module: account_tax_python
#. odoo-python
#: code:addons/account_tax_python/models/account_tax.py:0
#: code:addons/account_tax_python/models/account_tax.py:0
#, python-format
msgid ""
"You entered invalid code %r in %r taxes\n"
"\n"
"Error : %s"
msgstr ""
"Uneli ste nevažeći kod %r u %r poreze\n"
"\n"
"Greška: %s"
