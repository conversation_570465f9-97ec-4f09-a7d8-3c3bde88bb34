# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage of Price: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage of Price Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"    - Gruppo di imposte: l'imposta corrisponde a una serie di sottoimposte.\n"
"    - Fisso: l'importo imposta rimane uguale e indipendente dal prezzo.\n"
"    - Percentuale sul prezzo: l'importo imposta è una % del prezzo:\n"
"        es. 100 * (1 + 10%) = 110 (prezzo non incluso)\n"
"        es. 110 / (1 + 10%) = 100 (prezzo incluso)\n"
"    - Percentuale sul prezzo imposta inclusa: l'importa imposta è una frazione del prezzo:\n"
"        es. 180 / (1 - 10%) = 200 (prezzo non incluso)\n"
"        es. 200 * (1 - 10%) = 180 (prezzo incluso)\n"
"        "

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_applicable
msgid "Applicable Code"
msgstr "Codice applicabile"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Calcola l'importo dell'imposta impostando la variabile \"risultato\".\n"
"\n"
":param base_amount: a virgola mobile, importo effettivo a cui viene applicata l'imposta\n"
":param price_unit: a virgola mobile\n"
":param quantity: a virgola mobile\n"
":param company: singolo elemento di un insieme di record res.company\n"
":param product: singolo elemento di un insieme di record product.product oppure nessuno\n"
":param partner: singolo elemento di un insieme di record res.partner oppure nessuno"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"Determina se l'imposta viene applicata impostando la variabile \"risultato\" a vero o falso.\n"
"\n"
":param price_unit: a virgola mobile\n"
":param quantity: a virgola mobile\n"
":param company: singolo elemento di un insieme di record res.company\n"
":param product: singolo elemento di un insieme di record product.product oppure nessuno\n"
":param partner: singolo elemento di un insieme di record res.partner oppure nessuno"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_compute
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
msgid "Python Code"
msgstr "Codice Python"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "Imposta"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
msgid "Tax Computation"
msgstr "Calcolo imposta"

#. module: account_tax_python
#. odoo-python
#: code:addons/account_tax_python/models/account_tax.py:0
#: code:addons/account_tax_python/models/account_tax.py:0
#, python-format
msgid ""
"You entered invalid code %r in %r taxes\n"
"\n"
"Error : %s"
msgstr ""
"Codice inserito %r non valido per le imposte %r\n"
"\n"
"Errore : %s"
