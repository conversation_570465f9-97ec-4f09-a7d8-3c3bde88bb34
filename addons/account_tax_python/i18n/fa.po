# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_tax_python
# 
# Translators:
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>hory <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__amount_type
msgid ""
"\n"
"    - Group of Taxes: The tax is a set of sub taxes.\n"
"    - Fixed: The tax amount stays the same whatever the price.\n"
"    - Percentage of Price: The tax amount is a % of the price:\n"
"        e.g 100 * (1 + 10%) = 110 (not price included)\n"
"        e.g 110 / (1 + 10%) = 100 (price included)\n"
"    - Percentage of Price Tax Included: The tax amount is a division of the price:\n"
"        e.g 180 / (1 - 10%) = 200 (not price included)\n"
"        e.g 200 * (1 - 10%) = 180 (price included)\n"
"        "
msgstr ""
"\n"
"-گروه مالیات ها: مالیاتی که از زیر مجموعه هایی از مالیات هاست.\n"
"-ثابت: مقدار مالیات ثابت است صرف نظر از قیمت.\n"
"-درصد قیمت: مقدار مالیات درصدی از قیمت است:\n"
"    مثال: 100 * (1 + 10%) = 110 (شامل قیمت نمیشود)\n"
"    مثال: 110 / (1 + 10٪) = 100 (شامل قیمت میشود)\n"
"-درصد قیمت در مالیات شامل شده است: مقدار مالیات حاصل تقسیمی از قیمت است:\n"
"    مثال: 180 / (1 - 10٪) = 200 (شامل قیمت نمیشود)\n"
"    مثال: 200 * (1 - 10٪) = 180 (شامل قیمت میشود)\n"
"    "

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_applicable
msgid "Applicable Code"
msgstr "کد قابل اعمال"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_compute
msgid ""
"Compute the amount of the tax by setting the variable 'result'.\n"
"\n"
":param base_amount: float, actual amount on which the tax is applied\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"محاسبه‌ی مبلغ مالیات با تعیین «نتیجه‌ی» متغیر. \n"
": پارامتر مبلغ پایه: مبلغ واقعی شناوری که مالیات طبق آن اعمال می‌شود \n"
": پارامتر واحد قیمت: شناور\n"
": پارامتر مقدار: شناور\n"
": پارامتر شرکت: مجموعه‌ی تک عضوی رکورد res.company\n"
": پارامتر محصول: مجموعه‌ی تک عضوی رکورد  product.product یا هیچ\n"
": پارامتر شریک: مجموعه‌ی تک عضوی رکورد res.partner یا هیچ"

#. module: account_tax_python
#: model:ir.model.fields,help:account_tax_python.field_account_tax__python_applicable
msgid ""
"Determine if the tax will be applied by setting the variable 'result' to True or False.\n"
"\n"
":param price_unit: float\n"
":param quantity: float\n"
":param company: res.company recordset singleton\n"
":param product: product.product recordset singleton or None\n"
":param partner: res.partner recordset singleton or None"
msgstr ""
"مشخص کنید که آیا مالیات با تنظیم «نتیجه» روی درست یا نادرست اعمال می‌شود. \n"
": پارامتر واحد_قیمت: شناور\n"
": پارامتر کیفیت: شناور\n"
": پارامتر شرکت: مجموعه تک عضوی رکورد res.company\n"
": پارامتر محصول: مجموعه‌ی تک عضوی رکورد  product.product یا هیچ\n"
": پارامتر شریک: مجموعه تک‌عضوی رکورد  res.partner یا هیچ"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__python_compute
#: model:ir.model.fields.selection,name:account_tax_python.selection__account_tax__amount_type__code
msgid "Python Code"
msgstr "کد پایتون"

#. module: account_tax_python
#: model:ir.model,name:account_tax_python.model_account_tax
msgid "Tax"
msgstr "مالیات"

#. module: account_tax_python
#: model:ir.model.fields,field_description:account_tax_python.field_account_tax__amount_type
msgid "Tax Computation"
msgstr "محاسبه مالیات"

#. module: account_tax_python
#. odoo-python
#: code:addons/account_tax_python/models/account_tax.py:0
#: code:addons/account_tax_python/models/account_tax.py:0
#, python-format
msgid ""
"You entered invalid code %r in %r taxes\n"
"\n"
"Error : %s"
msgstr ""
"کد نامعتبر %r را در مالیات‌های %r وارد کرده‌اید\n"
"خطا: %s"
