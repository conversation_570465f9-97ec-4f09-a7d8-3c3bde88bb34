# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* analytic
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> CHEN <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_account.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s（副本）"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
msgid "<span class=\"o_stat_text\">Analytic Accounts</span>"
msgstr "<span class=\"o_stat_text\">分析账户</span>"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "<span class=\"o_stat_text\">Gross Margin</span>"
msgstr "<span class=\"o_stat_text\">毛利率</span>"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
#, python-format
msgid ""
"A 'Project' plan needs to exist and its id needs to be set as "
"`analytic.project_plan` in the system variables"
msgstr "\"项目\" 计划必须存在，其 id 必须在系统变量中设置为 \"analytic.project_plan\""

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Account field"
msgstr "账户字段"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__account_ids
msgid "Accounts"
msgstr "账户"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction
msgid "Action Needed"
msgstr "待处理"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__active
msgid "Active"
msgstr "已启用"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_active_account
msgid "Active account"
msgstr "启用账户"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
#, python-format
msgid "Add a Line"
msgstr "添加一行"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.action_account_analytic_account_form
#: model_terms:ir.actions.act_window,help:analytic.action_analytic_account_form
msgid "Add a new analytic account"
msgstr "添加一个新的分析账户"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_administratif
msgid "Administrative"
msgstr "管理"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__all_account_count
msgid "All Analytic Accounts Count"
msgstr "所有分析账户计数"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Amount"
msgstr "金额"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Amount field"
msgstr "账户字段"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
#, python-format
msgid "Analytic"
msgstr "分析"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_account
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__auto_account_id
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Analytic Account"
msgstr "分析账户"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_res_config_settings__group_analytic_accounting
#: model:res.groups,name:analytic.group_analytic_accounting
msgid "Analytic Accounting"
msgstr "分析会计"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Analytic Accounts"
msgstr "分析账户"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__account_count
msgid "Analytic Accounts Count"
msgstr "分析帐户计数"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__analytic_distribution
#: model:ir.model.fields,field_description:analytic.field_analytic_mixin__analytic_distribution
msgid "Analytic Distribution"
msgstr "分析分配"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#: model:ir.model,name:analytic.model_account_analytic_distribution_model
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_tree_view
#, python-format
msgid "Analytic Distribution Model"
msgstr "分析分配模型"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_distribution_model
msgid "Analytic Distribution Models"
msgstr "分析分配模型"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__analytic_distribution_search
#: model:ir.model.fields,field_description:analytic.field_analytic_mixin__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr "分析分配搜索"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Analytic Item"
msgstr "分析项目"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action_entries
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_graph
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_pivot
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Analytic Items"
msgstr "分析项目"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_line
msgid "Analytic Line"
msgstr "分析行"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__line_ids
msgid "Analytic Lines"
msgstr "分析行"

#. module: analytic
#: model:ir.model,name:analytic.model_analytic_mixin
msgid "Analytic Mixin"
msgstr "分析混入"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__analytic_plan_id
msgid "Analytic Plan"
msgstr "分析方案"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "分析方案的适用性"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_plan_action
#: model:ir.model,name:analytic.model_account_analytic_plan
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_tree_view
msgid "Analytic Plans"
msgstr "分析方案"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__analytic_precision
#: model:ir.model.fields,field_description:analytic.field_analytic_mixin__analytic_precision
msgid "Analytic Precision"
msgstr "分析精度"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
#, python-format
msgid "Analytical Accounts"
msgstr "分析帐户"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
#, python-format
msgid "Analytical Plans"
msgstr "分析方案"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__applicability
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__applicability_ids
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
msgid "Applicability"
msgstr "适用性"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Archived"
msgstr "已存档"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Associated Partner"
msgstr "关联的业务伙伴"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_asustek
msgid "Asustek"
msgstr "华硕"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_attachment_count
msgid "Attachment Count"
msgstr "附件计数"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__balance
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Balance"
msgstr "余额"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_kanban
msgid "Balance:"
msgstr "余额："

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Business domain"
msgstr "商业域名"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_partners_camp_to_camp
msgid "Camp to Camp"
msgstr "营地到营地"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_integration_c2c
msgid "CampToCamp"
msgstr "营地到营地"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__category
msgid "Category"
msgstr "类别"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_account_form
msgid "Chart of Analytic Accounts"
msgstr "分析会计账户表"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__children_count
msgid "Children Plans Count"
msgstr "下级方案计数"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__children_ids
msgid "Childrens"
msgstr "下级"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_plan_action
msgid "Click to add a new analytic account plan."
msgstr "点击添加新的分析帐户方案."

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
#, python-format
msgid "Close"
msgstr "关闭"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__color
msgid "Color"
msgstr "颜色"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__color
msgid "Color Index"
msgstr "颜色指标"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_commercial_marketing
msgid "Commercial & Marketing"
msgstr "商业和营销"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__company_id
msgid "Company"
msgstr "公司"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__complete_name
msgid "Complete Name"
msgstr "完整名称"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_form_view
msgid "Conditions to meet"
msgstr "需满足的条件"

#. module: analytic
#: model:ir.model,name:analytic.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr "仅同类别的计量单位可以互相转换。根据比例进行转换。"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Costs will be created automatically when you register supplier\n"
"                invoices, expenses or timesheets."
msgstr ""
"当您登记供应商结算单、费用或者计工单时\n"
"                会自动生成成本。"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__create_uid
msgid "Created by"
msgstr "创建人"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__create_date
msgid "Created on"
msgstr "创建日期"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__credit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Credit"
msgstr "收入"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__currency_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__currency_id
msgid "Currency"
msgstr "币种"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__partner_id
msgid "Customer"
msgstr "客户"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__date
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Date"
msgstr "日期"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__active
msgid "Deactivate the account."
msgstr "禁用帐户."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__debit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Debit"
msgstr "支出"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_agrolait
msgid "Deco Addict"
msgstr "装饰成瘾者"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__default_applicability
msgid "Default Applicability"
msgstr "默认范围"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_deltapc
msgid "Delta PC"
msgstr "台达电脑"

#. module: analytic
#: model:account.analytic.plan,name:analytic.analytic_plan_departments
msgid "Departments"
msgstr "部门"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__description
msgid "Description"
msgstr "描述"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_desertic_hispafuentes
msgid "Desertic - Hispafuentes"
msgstr "沙漠 - 伊斯帕富恩特斯"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Disable save"
msgstr "禁用保存"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_form_view
msgid "Distribution to apply"
msgstr "要应用的分布"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "域"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者（合作伙伴）"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Force applicability"
msgstr "强制适用性"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action
msgid "Gross Margin"
msgstr "毛利"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Group By..."
msgstr "分组原则......"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__has_message
msgid "Has Message"
msgstr "有消息"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__id
msgid "ID"
msgstr "ID"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction
msgid "If checked, new messages require your attention."
msgstr "如果勾选此项，则需要查看新消息。"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将出现发送错误。"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"In Odoo, sales orders and projects are implemented using\n"
"                analytic accounts. You can track costs and revenues to analyse\n"
"                your margins easily."
msgstr ""
"在 Odoo 中，使用\n"
"                分析会计实施销售订单和项目。您可以跟踪成本和收入，轻松分析\n"
"                您的利润。"

#. module: analytic
#: model:account.analytic.plan,name:analytic.analytic_plan_internal
msgid "Internal"
msgstr "内部"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_is_follower
msgid "Is Follower"
msgstr "是关注者"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_think_big_systems
msgid "Lumber Inc"
msgstr "木材公司"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_luminous_technologies
msgid "Luminous Technologies"
msgstr "发光技术"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__applicability__mandatory
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_plan__default_applicability__mandatory
msgid "Mandatory"
msgstr "必填"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error
msgid "Message Delivery error"
msgstr "消息发送错误"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_ids
msgid "Messages"
msgstr "消息"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_millennium_industries
msgid "Millennium Industries"
msgstr "Millennium Industries"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__business_domain__general
msgid "Miscellaneous"
msgstr "杂项"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__name
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Name"
msgstr "名称"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_nebula
msgid "Nebula"
msgstr "星云"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
#, python-format
msgid "New Model"
msgstr "新的模型"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid "No activity yet"
msgstr "还没有活动"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
msgid "No activity yet on this account"
msgstr "这个账户目前没有活动"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
#, python-format
msgid "No analytic plans found"
msgstr "未找到分析方案"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of Actions"
msgstr "操作数量"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of errors"
msgstr "错误数量"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要采取行动的消息数量"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息的数量"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_mixin.py:0
#, python-format
msgid "One or more lines require a 100% analytic distribution."
msgstr "一条或多条线需要 100% 的分析分配。"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_internal
msgid "Operating Costs"
msgstr "运营成本"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_mixin.py:0
#, python-format
msgid "Operation not supported"
msgstr "不支持的操作"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__applicability__optional
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_plan__default_applicability__optional
msgid "Optional"
msgstr "可选的"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_line__category__other
msgid "Other"
msgstr "其他"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_our_super_product
msgid "Our Super Product"
msgstr "我们的超级产品"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__parent_id
msgid "Parent"
msgstr "上级"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__parent_path
msgid "Parent Path"
msgstr "父级路径"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__partner_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "合作伙伴"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__partner_category_id
msgid "Partner Category"
msgstr "上级类型"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Percentage"
msgstr "百分比"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__plan_id
msgid "Plan"
msgstr "方案"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Product field"
msgstr "产品字段"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__account_id
msgid "Project Account"
msgstr "项目账户"

#. module: analytic
#: model:account.analytic.plan,name:analytic.analytic_plan_projects
msgid "Projects"
msgstr "项目"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__unit_amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Quantity"
msgstr "数量"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__code
msgid "Reference"
msgstr "参考号"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_rd_department
msgid "Research & Development"
msgstr "研究和发展"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Revenues will be created automatically when you create customer\n"
"                invoices. Customer invoices can be created based on sales orders\n"
"                (fixed price invoices), on timesheets (based on the work done) or\n"
"                on expenses (e.g. reinvoicing of travel costs)."
msgstr ""
"创建客户\n"
" 开票单时将自动创建收入。可基于销售订单\n"
" （固定价格开票单）、时间表（基于已完成工作）或\n"
" 费用（例如差旅费的再开票）创建客户开票单。"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__root_id
msgid "Root"
msgstr "根目录"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__root_plan_id
msgid "Root Plan"
msgstr "根方案"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
#, python-format
msgid "Save as new analytic distribution model"
msgstr "保存为新的分析分发模型"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_seagate_p2
msgid "Seagate P2"
msgstr "希捷 P2"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Search Analytic Lines"
msgstr "搜索分析明细行"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_distribution_model__company_id
msgid ""
"Select a company for which the analytic distribution will be used (e.g. "
"create new customer invoice or Sales order if we select this company, it "
"will automatically take this as an analytic account)"
msgstr "选择将使用分析分配的公司(例如，如果我们选择该公司，则创建新的客户发票或销售订单，它将自动将此作为分析帐户)"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_distribution_model__partner_category_id
msgid ""
"Select a partner category for which the analytic distribution will be used "
"(e.g. create new customer invoice or Sales order if we select this partner, "
"it will automatically take this as an analytic account)"
msgstr "选择将使用分析分配的合作伙伴类别（例如，如果我们选择此合作伙伴，则创建新的客户发票或销售订单，它将自动将此作为分析帐户)"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_distribution_model__partner_id
msgid ""
"Select a partner for which the analytic distribution will be used (e.g. "
"create new customer invoice or Sales order if we select this partner, it "
"will automatically take this as an analytic account)"
msgstr "选择将使用分析分配的合作伙伴（例如，如果我们选择此合作伙伴，则创建新的客户发票或销售订单，它将自动将此作为分析帐户)"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__sequence
msgid "Sequence"
msgstr "序列"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_spark
msgid "Spark Systems"
msgstr "火花系统"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
msgid "Subplans"
msgstr "下级方案"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_absences
msgid "Time Off"
msgstr "休假"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Total"
msgstr "总计"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__applicability__unavailable
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_plan__default_applicability__unavailable
msgid "Unavailable"
msgstr "不可用"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_id
msgid "Unit of Measure"
msgstr "计量单位"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_category_id
msgid "UoM Category"
msgstr "计量单位类别"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__user_id
msgid "User"
msgstr "用户"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_tree_view
msgid "View"
msgstr "视图"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_account.py:0
#, python-format
msgid ""
"You can't set a different company on your analytic account since there are "
"some analytic items linked to it."
msgstr "您不能在您的分析帐户上设置不同的公司，因为有一些分析项目关联到它."

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
#, python-format
msgid "You cannot add a parent to the base plan '%s'"
msgstr "你不可为基本计划 “%s” 加入母级项目"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_distribution_model.py:0
#, python-format
msgid ""
"You defined a distribution with analytic account(s) belonging to a specific "
"company but a model shared between companies or with a different company"
msgstr "您定义了属于特定公司的解析账户分布，但公司间或与其他公司共享模型"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "e.g. Project XYZ"
msgstr "例如：项目 XYZ"
