# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* analytic
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# cecilieflyt, 2024
# Rune Restad, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Rune Restad, 2024\n"
"Language-Team: <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_account.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopi)"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
msgid "<span class=\"o_stat_text\">Analytic Accounts</span>"
msgstr "<span class=\"o_stat_text\">Analytiske kontoer</span>"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "<span class=\"o_stat_text\">Gross Margin</span>"
msgstr "<span class=\"o_stat_text\">Bruttomargin</span>"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
#, python-format
msgid ""
"A 'Project' plan needs to exist and its id needs to be set as "
"`analytic.project_plan` in the system variables"
msgstr ""

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Account field"
msgstr "Kontofelt"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__account_ids
msgid "Accounts"
msgstr "Kontoer"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction
msgid "Action Needed"
msgstr "Handling påkrevet"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__active
msgid "Active"
msgstr "Aktiv"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_active_account
msgid "Active account"
msgstr "Aktiv konto"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
#, python-format
msgid "Add a Line"
msgstr "Legg til en linje"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.action_account_analytic_account_form
#: model_terms:ir.actions.act_window,help:analytic.action_analytic_account_form
msgid "Add a new analytic account"
msgstr "Opprett en ny analytisk konto"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_administratif
msgid "Administrative"
msgstr "Administrativ"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__all_account_count
msgid "All Analytic Accounts Count"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Amount"
msgstr "Beløp"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Amount field"
msgstr "Beløp felt"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
#, python-format
msgid "Analytic"
msgstr "Analytisk"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_account
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__auto_account_id
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Analytic Account"
msgstr "Analytisk konto"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_res_config_settings__group_analytic_accounting
#: model:res.groups,name:analytic.group_analytic_accounting
msgid "Analytic Accounting"
msgstr "Analytisk regnskap"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Analytic Accounts"
msgstr "Analytiske konti"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__account_count
msgid "Analytic Accounts Count"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__analytic_distribution
#: model:ir.model.fields,field_description:analytic.field_analytic_mixin__analytic_distribution
msgid "Analytic Distribution"
msgstr "Analytisk distribusjon"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#: model:ir.model,name:analytic.model_account_analytic_distribution_model
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_tree_view
#, python-format
msgid "Analytic Distribution Model"
msgstr "Analytisk distribusjonsmodell"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_distribution_model
msgid "Analytic Distribution Models"
msgstr "Analytiske distribusjonsmodeller"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__analytic_distribution_search
#: model:ir.model.fields,field_description:analytic.field_analytic_mixin__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr "Søk etter analytisk fordeling"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_form
msgid "Analytic Item"
msgstr "Analytisk registrering"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action_entries
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_graph
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_pivot
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Analytic Items"
msgstr "Analytiske registreringer"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytisk linje"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__line_ids
msgid "Analytic Lines"
msgstr "Analytiske linjer"

#. module: analytic
#: model:ir.model,name:analytic.model_analytic_mixin
msgid "Analytic Mixin"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__analytic_plan_id
msgid "Analytic Plan"
msgstr "Analytisk plan"

#. module: analytic
#: model:ir.model,name:analytic.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Analyseringsplanens anvendeligheter."

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_plan_action
#: model:ir.model,name:analytic.model_account_analytic_plan
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_tree_view
msgid "Analytic Plans"
msgstr "Analytiske planer"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__analytic_precision
#: model:ir.model.fields,field_description:analytic.field_analytic_mixin__analytic_precision
msgid "Analytic Precision"
msgstr "Analytisk presisjon"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
#, python-format
msgid "Analytical Accounts"
msgstr "Analytiske kontoer"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
#, python-format
msgid "Analytical Plans"
msgstr "Analytiske planer"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__applicability
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__applicability_ids
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
msgid "Applicability"
msgstr "Anvendbarhet"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Archived"
msgstr "Arkivert"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
msgid "Associated Partner"
msgstr "Samarbeidspartner"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_asustek
msgid "Asustek"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_attachment_count
msgid "Attachment Count"
msgstr "Antall vedlegg"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__balance
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Balance"
msgstr "Balanse"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_kanban
msgid "Balance:"
msgstr "Balanse:"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Business domain"
msgstr ""

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_partners_camp_to_camp
msgid "Camp to Camp"
msgstr ""

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_integration_c2c
msgid "CampToCamp"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__category
msgid "Category"
msgstr "Kategori"

#. module: analytic
#: model:ir.actions.act_window,name:analytic.action_analytic_account_form
msgid "Chart of Analytic Accounts"
msgstr "Analytiske konti"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__children_count
msgid "Children Plans Count"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__children_ids
msgid "Childrens"
msgstr "Barn"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_plan_action
msgid "Click to add a new analytic account plan."
msgstr "Klikk for å legge til en ny analytisk kontoplan"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
#, python-format
msgid "Close"
msgstr "Lukk"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__color
msgid "Color"
msgstr "Farge"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__color
msgid "Color Index"
msgstr "Fargeindeks"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_commercial_marketing
msgid "Commercial & Marketing"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__company_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__company_id
msgid "Company"
msgstr "Firma"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__complete_name
msgid "Complete Name"
msgstr "Fullstendig navn"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_form_view
msgid "Conditions to meet"
msgstr "Betingelser som må dekkes"

#. module: analytic
#: model:ir.model,name:analytic.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Konvertering mellom enheter fungerer kun når de tilhører samme kategori. "
"Konverteringen gjøres basert på forholdet mellom enhetene."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Costs will be created automatically when you register supplier\n"
"                invoices, expenses or timesheets."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__create_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__create_date
msgid "Created on"
msgstr "Opprettet den"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__credit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Credit"
msgstr "Kredit"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__currency_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__partner_id
msgid "Customer"
msgstr "Kunde"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__date
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Date"
msgstr "Dato"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__active
msgid "Deactivate the account."
msgstr "Deaktiver konto"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__debit
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Debit"
msgstr "Debit"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_agrolait
msgid "Deco Addict"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__default_applicability
msgid "Default Applicability"
msgstr "Standard anvendelse"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_deltapc
msgid "Delta PC"
msgstr ""

#. module: analytic
#: model:account.analytic.plan,name:analytic.analytic_plan_departments
msgid "Departments"
msgstr "Avdelinger"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__description
msgid "Description"
msgstr "Beskrivelse"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_desertic_hispafuentes
msgid "Desertic - Hispafuentes"
msgstr ""

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Disable save"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__display_name
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_form_view
msgid "Distribution to apply"
msgstr "Distribusjon å bruke"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Domene"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Force applicability"
msgstr ""

#. module: analytic
#: model:ir.actions.act_window,name:analytic.account_analytic_line_action
msgid "Gross Margin"
msgstr "Brutto fortjeneste"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_search
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Group By..."
msgstr "Grupper etter..."

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__has_message
msgid "Has Message"
msgstr "Har melding"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__id
msgid "ID"
msgstr "ID"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Hvis haket av, vil nye meldinger kreve din oppmerksomhet."

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis haket av, har enkelte meldinger leveringsfeil."

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"In Odoo, sales orders and projects are implemented using\n"
"                analytic accounts. You can track costs and revenues to analyse\n"
"                your margins easily."
msgstr ""

#. module: analytic
#: model:account.analytic.plan,name:analytic.analytic_plan_internal
msgid "Internal"
msgstr "Intern"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_uid
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_applicability__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__write_date
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_think_big_systems
msgid "Lumber Inc"
msgstr ""

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_luminous_technologies
msgid "Luminous Technologies"
msgstr ""

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__applicability__mandatory
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_plan__default_applicability__mandatory
msgid "Mandatory"
msgstr "Obligatorisk"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error
msgid "Message Delivery error"
msgstr "Melding ved leveringsfeil"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_ids
msgid "Messages"
msgstr "Meldinger"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_millennium_industries
msgid "Millennium Industries"
msgstr ""

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__business_domain__general
msgid "Miscellaneous"
msgstr "Diverse"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__name
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_list
msgid "Name"
msgstr "Navn"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_nebula
msgid "Nebula"
msgstr ""

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
#, python-format
msgid "New Model"
msgstr "Ny modell"

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid "No activity yet"
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
msgid "No activity yet on this account"
msgstr ""

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
#, python-format
msgid "No analytic plans found"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of Actions"
msgstr "Antall handlinger"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of errors"
msgstr "Antall feil"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antall beskjeder som trenger oppfølging"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_account__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antall meldinger med leveringsfeil"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_mixin.py:0
#, python-format
msgid "One or more lines require a 100% analytic distribution."
msgstr ""

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_internal
msgid "Operating Costs"
msgstr "Operasjonskostnader"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_mixin.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operasjon ikke støttet"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__applicability__optional
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_plan__default_applicability__optional
msgid "Optional"
msgstr "Valgfri"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_line__category__other
msgid "Other"
msgstr "Annen"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_our_super_product
msgid "Our Super Product"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__parent_id
msgid "Parent"
msgstr "Overordnet"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__parent_path
msgid "Parent Path"
msgstr "Overordnet sti"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__partner_id
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__partner_id
msgid "Partner"
msgstr "Partner"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_distribution_model__partner_category_id
msgid "Partner Category"
msgstr ""

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Percentage"
msgstr "Prosent"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__plan_id
msgid "Plan"
msgstr "Planlegg"

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.js:0
#, python-format
msgid "Product field"
msgstr "Produktfelt"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__account_id
msgid "Project Account"
msgstr "Prosjektkonto"

#. module: analytic
#: model:account.analytic.plan,name:analytic.analytic_plan_projects
msgid "Projects"
msgstr "Prosjekter"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__unit_amount
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Quantity"
msgstr "Antall"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__code
msgid "Reference"
msgstr "Referanse"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_rd_department
msgid "Research & Development"
msgstr ""

#. module: analytic
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action
#: model_terms:ir.actions.act_window,help:analytic.account_analytic_line_action_entries
msgid ""
"Revenues will be created automatically when you create customer\n"
"                invoices. Customer invoices can be created based on sales orders\n"
"                (fixed price invoices), on timesheets (based on the work done) or\n"
"                on expenses (e.g. reinvoicing of travel costs)."
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__root_id
msgid "Root"
msgstr "Rot"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_account__root_plan_id
msgid "Root Plan"
msgstr ""

#. module: analytic
#. odoo-javascript
#: code:addons/analytic/static/src/components/analytic_distribution/analytic_distribution.xml:0
#, python-format
msgid "Save as new analytic distribution model"
msgstr ""

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_seagate_p2
msgid "Seagate P2"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_filter
msgid "Search Analytic Lines"
msgstr "Søk analytiske linjer"

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_distribution_model__company_id
msgid ""
"Select a company for which the analytic distribution will be used (e.g. "
"create new customer invoice or Sales order if we select this company, it "
"will automatically take this as an analytic account)"
msgstr ""

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_distribution_model__partner_category_id
msgid ""
"Select a partner category for which the analytic distribution will be used "
"(e.g. create new customer invoice or Sales order if we select this partner, "
"it will automatically take this as an analytic account)"
msgstr ""

#. module: analytic
#: model:ir.model.fields,help:analytic.field_account_analytic_distribution_model__partner_id
msgid ""
"Select a partner for which the analytic distribution will be used (e.g. "
"create new customer invoice or Sales order if we select this partner, it "
"will automatically take this as an analytic account)"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_plan__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_spark
msgid "Spark Systems"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_plan_form_view
msgid "Subplans"
msgstr ""

#. module: analytic
#: model:account.analytic.account,name:analytic.analytic_absences
msgid "Time Off"
msgstr "Fravær"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_line_tree
msgid "Total"
msgstr "Total"

#. module: analytic
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_applicability__applicability__unavailable
#: model:ir.model.fields.selection,name:analytic.selection__account_analytic_plan__default_applicability__unavailable
msgid "Unavailable"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_id
msgid "Unit of Measure"
msgstr "Enhet"

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__product_uom_category_id
msgid "UoM Category"
msgstr ""

#. module: analytic
#: model:ir.model.fields,field_description:analytic.field_account_analytic_line__user_id
msgid "User"
msgstr "Bruker"

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.account_analytic_distribution_model_tree_view
msgid "View"
msgstr "Vis"

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_account.py:0
#, python-format
msgid ""
"You can't set a different company on your analytic account since there are "
"some analytic items linked to it."
msgstr ""

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_plan.py:0
#, python-format
msgid "You cannot add a parent to the base plan '%s'"
msgstr ""

#. module: analytic
#. odoo-python
#: code:addons/analytic/models/analytic_distribution_model.py:0
#, python-format
msgid ""
"You defined a distribution with analytic account(s) belonging to a specific "
"company but a model shared between companies or with a different company"
msgstr ""

#. module: analytic
#: model_terms:ir.ui.view,arch_db:analytic.view_account_analytic_account_form
msgid "e.g. Project XYZ"
msgstr "for eksempel Prosjekt XYZ"
