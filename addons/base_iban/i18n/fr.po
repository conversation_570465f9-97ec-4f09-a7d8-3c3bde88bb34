# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_iban
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_iban
#. odoo-javascript
#: code:addons/base_iban/static/src/components/iban_widget/iban_widget.xml:0
#, python-format
msgid "Account isn't a valid IBAN"
msgstr "Le compte n'est pas un IBAN valide"

#. module: base_iban
#: model:ir.model,name:base_iban.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Comptes bancaires"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "Cannot compute the BBAN because the account number is not an IBAN."
msgstr ""
"Impossible de calculer le BBAN car le numéro de compte n'est pas un IBAN."

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "IBAN"
msgstr "IBAN"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid ""
"The IBAN does not seem to be correct. You should have entered something like this %s\n"
"Where B = National bank code, S = Branch code, C = Account No, k = Check digit"
msgstr ""
"L'IBAN semble incorrect. La saisie doit être de la forme : %s\n"
"Où B = Code national de la banque, S = Code de la branche, C = Numéro de compte, k = Chiffres de vérification"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "The IBAN is invalid, it should begin with the country code"
msgstr "L'IBAN est invalide, il doit commencer par le code du pays"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "There is no IBAN code."
msgstr "Il n'y a pas de code IBAN."

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "This IBAN does not pass the validation check, please verify it."
msgstr ""
"Cet IBAN ne passe pas la vérification de validité, veuillez le vérifier."
