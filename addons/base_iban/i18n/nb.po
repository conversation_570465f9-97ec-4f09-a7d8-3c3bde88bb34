# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_iban
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_iban
#. odoo-javascript
#: code:addons/base_iban/static/src/components/iban_widget/iban_widget.xml:0
#, python-format
msgid "Account isn't a valid IBAN"
msgstr "Kontoen er ikke gyldig IBAN"

#. module: base_iban
#: model:ir.model,name:base_iban.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Bankkonti"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "Cannot compute the BBAN because the account number is not an IBAN."
msgstr "Kan ikke beregne BBAN fordi kontonummeret ikke er en IBAN."

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "IBAN"
msgstr "IBAN"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid ""
"The IBAN does not seem to be correct. You should have entered something like this %s\n"
"Where B = National bank code, S = Branch code, C = Account No, k = Check digit"
msgstr ""
"IBAN ser ikke ut til å være korrekt. Du skulle ha lagt inn noe som likner på dette: %s\n"
"Der B = nasjonal bankkode, S = avdelingskode C = kontonummer og k = sjekktall"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "The IBAN is invalid, it should begin with the country code"
msgstr "IBAN er ugyldig, den skal begynne med landkode"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "There is no IBAN code."
msgstr "Det finnes ingen IBAN kode."

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "This IBAN does not pass the validation check, please verify it."
msgstr "IBAN kan ikke valideres, vennligst sjekk at den er korrekt."
