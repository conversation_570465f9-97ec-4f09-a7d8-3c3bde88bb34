# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_iban
# 
# Translators:
# <PERSON>, 2023
# fardin mardani, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: fardin mardani, 2023\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: base_iban
#. odoo-javascript
#: code:addons/base_iban/static/src/components/iban_widget/iban_widget.xml:0
#, python-format
msgid "Account isn't a valid IBAN"
msgstr "حساب یک IBAN معتبر نیست"

#. module: base_iban
#: model:ir.model,name:base_iban.model_res_partner_bank
msgid "Bank Accounts"
msgstr "حساب‌های بانکی"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "Cannot compute the BBAN because the account number is not an IBAN."
msgstr "نمی توان BBAN را محاسبه کرد زیرا شماره حساب IBAN نیست."

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "IBAN"
msgstr "IBAN"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid ""
"The IBAN does not seem to be correct. You should have entered something like this %s\n"
"Where B = National bank code, S = Branch code, C = Account No, k = Check digit"
msgstr ""
"به نظر نمی رسد IBAN صحیح باشد. شما باید چیزی شبیه به این را وارد می کردید%s\n"
"جایی که B = کد بانک ملی، S = کد شعبه، C = شماره حساب، k = رقم چک"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "The IBAN is invalid, it should begin with the country code"
msgstr "IBAN نامعتبر است، باید با کد کشور شروع شود"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "There is no IBAN code."
msgstr "هیچ کد IBAN وجود ندارد."

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
#, python-format
msgid "This IBAN does not pass the validation check, please verify it."
msgstr "این IBAN از بررسی اعتبار سنجی عبور نمی کند، لطفاً آن را تأیید کنید."
