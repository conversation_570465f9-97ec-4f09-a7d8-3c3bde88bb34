# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_mail
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: auth_totp_mail
#: model:mail.template,body_html:auth_totp_mail.mail_template_totp_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\"></t><br><br>\n"
"        <t t-out=\"user.name  or ''\"></t> requested you activate two-factor authentication to protect your account.<br><br>\n"
"        Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"        The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"        Popular ones include Authy, Google Authenticator or the Microsoft Authenticator.\n"
"\n"
"        <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                Activate my two-factor Authentication\n"
"            </a>\n"
"        </p>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Gentile <t t-out=\"object.partner_id.name or ''\"></t><br><br>\n"
"        <t t-out=\"user.name  or ''\"></t> ha chiesto di attivare l'autenticazione a due fattori per proteggere l'account.<br><br>\n"
"        L'autenticazione a due fattori (\"2FA\") è un sistema di doppia autenticazione.\n"
"        La prima viene effettuata attraverso la password mentre la seconda con un condice che si ottiene da un'app specifica per dispositivi mobili.\n"
"        Tra le più famose ci sono Authy, Google Authenticator o Microsoft Authenticator.\n"
"\n"
"        <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                Attiva l'autenticazione a due fattori\n"
"            </a>\n"
"        </p>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.account_security_setting_update
msgid "<span>Consider</span>"
msgstr "<span>Considera</span>"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
#, python-format
msgid "A trusted device has just been added to your account: %(device_name)s"
msgstr ""
"È stato appena aggiunto un dispositivo sicuro al tuo account: "
"%(device_name)s"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
#, python-format
msgid ""
"A trusted device has just been removed from your account: %(device_names)s"
msgstr ""
"È stato appena rimosso un dispositivo sicuro dal tuo account: "
"%(device_names)s"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Account Security"
msgstr "Sicurezza account"

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_auth_totp_device
msgid "Authentication Device"
msgstr "Dispositivo di autenticazione"

#. module: auth_totp_mail
#: model:mail.template,subject:auth_totp_mail.mail_template_totp_invite
msgid "Invitation to activate two-factor authentication on your Odoo account"
msgstr ""
"Invito ad attivare l'autenticazione a due fattori sul tuo account Odoo"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid ""
"Invitation to use two-factor authentication sent for the following user(s): "
"%s"
msgstr ""
"Invito ad usare l'autenticazione a due fattori inviato per i seguenti "
"utenti: %s"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.view_users_form
msgid "Invite to use 2FA"
msgstr "Invito ad usare 2FA"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_invite_totp
msgid "Invite to use two-factor authentication"
msgstr "Invito a usare l'autenticazione a due fattori"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.res_users_view_form
msgid "Name"
msgstr "Nome"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_activate_two_factor_authentication
msgid "Open two-factor authentication configuration"
msgstr "Aprire la configurazione dell'autenticazione a due fattori"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Security Update: 2FA Activated"
msgstr "Aggiornamento sicurezza: 2FA attivata"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Security Update: 2FA Deactivated"
msgstr "Aggiornamento sicurezza: 2FA disattivata"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
#, python-format
msgid "Security Update: Device Added"
msgstr "Aggiornamento della sicurezza: dispositivo aggiunto"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
#, python-format
msgid "Security Update: Device Removed"
msgstr "Aggiornamento della sicurezza: dispositivo rimosso"

#. module: auth_totp_mail
#: model:mail.template,name:auth_totp_mail.mail_template_totp_invite
msgid "Settings: 2Fa Invitation"
msgstr "Impostazioni: Invito 2FA"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Two-factor authentication has been activated on your account"
msgstr "L'autenticazione a due fattori è stata attivata per il tuo account"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Two-factor authentication has been deactivated on your account"
msgstr "L'autenticazione a due fattori è stata disattivata per il tuo account"

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_res_users
msgid "User"
msgstr "Utente"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.account_security_setting_update
msgid "activating Two-factor Authentication"
msgstr "attivando l'autenticazione a due fattori"
