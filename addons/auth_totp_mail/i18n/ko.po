# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_mail
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_totp_mail
#: model:mail.template,body_html:auth_totp_mail.mail_template_totp_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\"></t><br><br>\n"
"        <t t-out=\"user.name  or ''\"></t> requested you activate two-factor authentication to protect your account.<br><br>\n"
"        Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"        The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"        Popular ones include Authy, Google Authenticator or the Microsoft Authenticator.\n"
"\n"
"        <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                Activate my two-factor Authentication\n"
"            </a>\n"
"        </p>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        안녕하세요, <t t-out=\"object.partner_id.name or ''\"></t>님.<br><br>\n"
"        <t t-out=\"user.name  or ''\"></t>님께서 귀하의 계정 보호를 위해 2단계 인증을 활성화하도록 요청하셨습니다.<br><br>\n"
"        2단계 인증 절차(\"2FA\")는 이중 확인 절차를 통해 사용자 본인만이 계정에 접근할 수 있도록 하는 인증 시스템입니다..\n"
"        아이디와 비밀번호로 로그인한 후, 사전에 등록했던 모바일 앱을 통해 받은 코드를 입력하면 추가 인증이 이루어집니다.\n"
"        인증 앱으로는 Authy, Google Authenticator 또는 Microsoft Authenticator가 사용되고 있습니다.\n"
"\n"
"        <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                2단계 인증 활성화하기\n"
"            </a>\n"
"        </p>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.account_security_setting_update
msgid "<span>Consider</span>"
msgstr "<span>계정을 안전하게 보호하기 위해 2단계 인증을 사용해 보세요.</span>"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
#, python-format
msgid "A trusted device has just been added to your account: %(device_name)s"
msgstr "계정에 신뢰할 수 있는 장치가 추가되었습니다: %(device_name)s"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
#, python-format
msgid ""
"A trusted device has just been removed from your account: %(device_names)s"
msgstr "계정에서 신뢰할 수 있는 장치가 제거되었습니다: %(device_names)s"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Account Security"
msgstr "계정 보안"

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_auth_totp_device
msgid "Authentication Device"
msgstr "인증 장치"

#. module: auth_totp_mail
#: model:mail.template,subject:auth_totp_mail.mail_template_totp_invite
msgid "Invitation to activate two-factor authentication on your Odoo account"
msgstr "Odoo 계정에서 2단계 인증을 활성화하도록 초대합니다"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid ""
"Invitation to use two-factor authentication sent for the following user(s): "
"%s"
msgstr "다음 사용자에 대해 2단계 인증 사용 초대를 보냈습니다: %s"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.view_users_form
msgid "Invite to use 2FA"
msgstr "2FA 사용 초대"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_invite_totp
msgid "Invite to use two-factor authentication"
msgstr "2단계 인증 설정 초대"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.res_users_view_form
msgid "Name"
msgstr "이름"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_activate_two_factor_authentication
msgid "Open two-factor authentication configuration"
msgstr "2단계 인증 환경설정 열기"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Security Update: 2FA Activated"
msgstr "보안 업데이트: 2FA 활성화됨"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Security Update: 2FA Deactivated"
msgstr "보안 업데이트: 2FA 비활성화됨"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
#, python-format
msgid "Security Update: Device Added"
msgstr "보안 업데이트: 장치 추가됨"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
#, python-format
msgid "Security Update: Device Removed"
msgstr "보안 업데이트: 장치 제거됨"

#. module: auth_totp_mail
#: model:mail.template,name:auth_totp_mail.mail_template_totp_invite
msgid "Settings: 2Fa Invitation"
msgstr "설정: 2Fa 초대"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Two-factor authentication has been activated on your account"
msgstr "계정에서 2단계 인증이 활성화되었습니다 "

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Two-factor authentication has been deactivated on your account"
msgstr "계정에서 2단계 인증이 비활성화되었습니다"

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_res_users
msgid "User"
msgstr "사용자"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.account_security_setting_update
msgid "activating Two-factor Authentication"
msgstr "2단계 인증 설정하기"
