# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_mail
# 
# Translators:
# <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# <PERSON> <mbo<PERSON><PERSON>@outlook.com>, 2023
# コフスタジオ, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: コフスタジオ, 2024\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: auth_totp_mail
#: model:mail.template,body_html:auth_totp_mail.mail_template_totp_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or ''\"></t><br><br>\n"
"        <t t-out=\"user.name  or ''\"></t> requested you activate two-factor authentication to protect your account.<br><br>\n"
"        Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"        The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"        Popular ones include Authy, Google Authenticator or the Microsoft Authenticator.\n"
"\n"
"        <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                Activate my two-factor Authentication\n"
"            </a>\n"
"        </p>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Poštovani <t t-out=\"object.partner_id.name or ''\"></t><br><br>\n"
"        <t t-out=\"user.name  or ''\"></t> zatražio da aktivirate dvofaktornu autentifikaciju da biste zaštitili svoj nalog.<br><br>\n"
"        Dvofaktorska autentifikacija („2FA“) je sistem dvostruke autentifikacije.\n"
"        Prvi se radi pomoću vaše lozinke, a drugi kodom koji dobijate iz namenske mobilne aplikacije.\n"
"        Popularni uključuju Authi, Google Authenticator ili Microsoft Authenticator.\n"
"\n"
"        <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"            <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                Aktivirajte moju dvofaktorsku autentifikaciju\n"
"            </a>\n"
"        </p>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.account_security_setting_update
msgid "<span>Consider</span>"
msgstr "<span>Razmotri</span>"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
#, python-format
msgid "A trusted device has just been added to your account: %(device_name)s"
msgstr "Dodat je pouzdan uređaj na vaš nalog: %(device_name)s"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
#, python-format
msgid ""
"A trusted device has just been removed from your account: %(device_names)s"
msgstr ""
"Jedan pouzdan uređaj je upravo uklonjen sa vašeg naloga: %(device_names)s"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Account Security"
msgstr "Bezbednost naloga"

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_auth_totp_device
msgid "Authentication Device"
msgstr "Autentifikacioni uređaj"

#. module: auth_totp_mail
#: model:mail.template,subject:auth_totp_mail.mail_template_totp_invite
msgid "Invitation to activate two-factor authentication on your Odoo account"
msgstr ""
"Poziv za aktiviranje dvofaktorske autentifikacije na vašem Odoo nalogu"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid ""
"Invitation to use two-factor authentication sent for the following user(s): "
"%s"
msgstr ""
"Poziv za korišćenje dvofaktorske autentifikacije je poslat za sledeće "
"korisnike: %s"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.view_users_form
msgid "Invite to use 2FA"
msgstr "Pozovite da koristite 2FA"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_invite_totp
msgid "Invite to use two-factor authentication"
msgstr "Pozovite da koristite dvofaktornu autentifikaciju"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.res_users_view_form
msgid "Name"
msgstr "Naziv"

#. module: auth_totp_mail
#: model:ir.actions.server,name:auth_totp_mail.action_activate_two_factor_authentication
msgid "Open two-factor authentication configuration"
msgstr "Otvorena dvofaktorska konfiguracija autentifikacije"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Security Update: 2FA Activated"
msgstr "Bezbednosno ažuriranje: 2FA aktivirana"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Security Update: 2FA Deactivated"
msgstr "Sigurnosno ažuriranje: 2FA deaktivirana"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
#, python-format
msgid "Security Update: Device Added"
msgstr "Bezbednosno ažuriranje: uređaj dodat"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/auth_totp_device.py:0
#, python-format
msgid "Security Update: Device Removed"
msgstr "Bezbednosno ažuriranje: uređaj uklonjen"

#. module: auth_totp_mail
#: model:mail.template,name:auth_totp_mail.mail_template_totp_invite
msgid "Settings: 2Fa Invitation"
msgstr "Podešavanja: 2FA pozivnice"

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Two-factor authentication has been activated on your account"
msgstr "Dvofaktorska autentifikacija je aktivirana na vašem nalogu."

#. module: auth_totp_mail
#. odoo-python
#: code:addons/auth_totp_mail/models/res_users.py:0
#, python-format
msgid "Two-factor authentication has been deactivated on your account"
msgstr "Dvofaktorska autentifikacija je deaktivirana na vašem nalogu."

#. module: auth_totp_mail
#: model:ir.model,name:auth_totp_mail.model_res_users
msgid "User"
msgstr "Korisnik"

#. module: auth_totp_mail
#: model_terms:ir.ui.view,arch_db:auth_totp_mail.account_security_setting_update
msgid "activating Two-factor Authentication"
msgstr "aktiviranje Dvofaktorske autentifikacije"
