# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_geolocalize
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_id
msgid "API"
msgstr "API"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/base_geocoder.py:0
#, python-format
msgid ""
"API key for GeoCoding (Places) required.\n"
"Visit https://developers.google.com/maps/documentation/geocoding/get-api-key for more information."
msgstr ""
"API-Schlüssel für GeoCoding (Orte) erforderlich.\n"
"Besuchen Sie https://developers.google.com/maps/documentation/geocoding/get-api-key für mehr Informationen."

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.res_config_settings_view_form
msgid "API:"
msgstr "API:"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Compute Localization"
msgstr "Lokalisierung berechnen"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Compute based on address"
msgstr "Anhand der Adresse berechnen"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/base_geocoder.py:0
#, python-format
msgid "Error with geolocation server:"
msgstr "Fehler beim Geolokalisierungsserver:"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_base_geocoder
msgid "Geo Coder"
msgstr "Geocoder"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geo Location"
msgstr "Geo-Standort"

#. module: base_geolocalize
#: model:ir.model,name:base_geolocalize.model_base_geo_provider
msgid "Geo Provider"
msgstr "Geo-Anbieter"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Geolocation"
msgstr "Geolokalisierung"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_partner__date_localization
#: model:ir.model.fields,field_description:base_geolocalize.field_res_users__date_localization
msgid "Geolocation Date"
msgstr "Datum der Geolokalisierung"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_googlemap_key
msgid "Google Map API Key"
msgstr "API-Schlüssel von Google Maps"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__id
msgid "ID"
msgstr "ID"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.res_config_settings_view_form
msgid "Key:"
msgstr "Schlüssel:"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Lat :"
msgstr "Breitengrad:"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Long:"
msgstr "Längengrad:"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__name
msgid "Name"
msgstr "Name"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/res_partner.py:0
#, python-format
msgid "No match found for %(partner_names)s address(es)."
msgstr "Keine Übereinstimmung für Adresse(n) von %(partner_names)s gefunden."

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Partner Assignment"
msgstr "Partnerzuweisung"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/base_geocoder.py:0
#, python-format
msgid "Provider %s is not implemented for geolocation service."
msgstr "Anbieter %s ist für den Geolokalisierungsdienst nicht implementiert."

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Refresh"
msgstr "Aktualisieren"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Refresh Localization"
msgstr "Lokalisierung aktualisieren"

#. module: base_geolocalize
#: model:ir.model.fields,field_description:base_geolocalize.field_base_geo_provider__tech_name
#: model:ir.model.fields,field_description:base_geolocalize.field_res_config_settings__geoloc_provider_techname
msgid "Technical Name"
msgstr "Technische Bezeichnung"

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/base_geocoder.py:0
#, python-format
msgid ""
"Unable to geolocate, received the error:\n"
"%s\n"
"\n"
"Google made this a paid feature.\n"
"You should first enable billing on your Google account.\n"
"Then, go to Developer Console, and enable the APIs:\n"
"Geocoding, Maps Static, Maps Javascript.\n"
msgstr ""
"Geolokalisierung nicht möglich, erhielt den Fehler:\n"
"%s\n"
"\n"
"Google hat dies zu einer kostenpflichtigen Funktion gemacht.\n"
"Sie sollten zunächst die Abrechnung in Ihrem Google-Konto aktivieren.\n"
"Gehen Sie dann zur Entwicklerkonsole und aktivieren Sie die APIs:\n"
"Geocoding, Maps Static, Maps Javascript.\n"

#. module: base_geolocalize
#: model_terms:ir.ui.view,arch_db:base_geolocalize.view_crm_partner_geo_form
msgid "Updated on:"
msgstr "Aktualisiert am:"

#. module: base_geolocalize
#: model:ir.model.fields,help:base_geolocalize.field_res_config_settings__geoloc_provider_googlemap_key
msgid ""
"Visit https://developers.google.com/maps/documentation/geocoding/get-api-key"
" for more information."
msgstr ""
"Besuchen Sie https://developers.google.com/maps/documentation/geocoding/get-"
"api-key für mehr Informationen."

#. module: base_geolocalize
#. odoo-python
#: code:addons/base_geolocalize/models/res_partner.py:0
#, python-format
msgid "Warning"
msgstr "Warnung"
