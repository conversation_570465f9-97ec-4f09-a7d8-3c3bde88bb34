# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_sparse_field
# 
# Translators:
# Wil O<PERSON>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_base
msgid "Base"
msgstr "Base"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__boolean
msgid "Boolean"
msgstr "Booleano"

#. module: base_sparse_field
#. odoo-python
#: code:addons/base_sparse_field/models/models.py:0
#, python-format
msgid "Changing the storing system for field \"%s\" is not allowed."
msgstr ""
"No está permitido cambiar el sistema de almacenamiento para el campo \"%s\"."

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__char
msgid "Char"
msgstr "Carácter"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__create_date
msgid "Created on"
msgstr "Creado el"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__data
msgid "Data"
msgstr "Datos"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_ir_model_fields__ttype
msgid "Field Type"
msgstr "Tipo de campo"

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_ir_model_fields
msgid "Fields"
msgstr "Campos"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__float
msgid "Float"
msgstr "Número flotante"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__id
msgid "ID"
msgstr "ID"

#. module: base_sparse_field
#: model:ir.model.fields,help:base_sparse_field.field_ir_model_fields__serialization_field_id
msgid ""
"If set, this field will be stored in the sparse structure of the "
"serialization field, instead of having its own database column. This cannot "
"be changed after creation."
msgstr ""
"Si está configurado así, este campo se almacenará en la estructura del campo"
" de serialización en lugar de tener su propia columna en la base de datos. "
"No se puede modificar después de crearlo."

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__integer
msgid "Integer"
msgstr "Entero"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: base_sparse_field
#: model:ir.model.fields.selection,name:base_sparse_field.selection__sparse_fields_test__selection__one
msgid "One"
msgstr "Uno"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__partner
msgid "Partner"
msgstr "Contacto"

#. module: base_sparse_field
#. odoo-python
#: code:addons/base_sparse_field/models/models.py:0
#, python-format
msgid "Renaming sparse field \"%s\" is not allowed"
msgstr "No está permitido renombrar el campo disperso \"%s\""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__selection
msgid "Selection"
msgstr "Selección"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_ir_model_fields__serialization_field_id
msgid "Serialization Field"
msgstr "Campo de serialización"

#. module: base_sparse_field
#. odoo-python
#: code:addons/base_sparse_field/models/models.py:0
#, python-format
msgid "Serialization field %r not found for sparse field %s!"
msgstr ""
"¡No se encontró el campo de serialización %r para el campo disperso %s!"

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_sparse_fields_test
msgid "Sparse fields Test"
msgstr "Prueba de campos dispersos"

#. module: base_sparse_field
#: model:ir.model.fields.selection,name:base_sparse_field.selection__sparse_fields_test__selection__two
msgid "Two"
msgstr "Dos"

#. module: base_sparse_field
#: model:ir.model.fields.selection,name:base_sparse_field.selection__ir_model_fields__ttype__serialized
msgid "serialized"
msgstr "serializado"
