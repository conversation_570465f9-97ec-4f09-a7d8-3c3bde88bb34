# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_sparse_field
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-07-12 07:50+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: French (Canada) (https://www.transifex.com/odoo/teams/41243/fr_CA/)\n"
"Language: fr_CA\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_base
msgid "Base"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__boolean
msgid "Boolean"
msgstr ""

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:0
#, python-format
msgid "Changing the storing system for field \"%s\" is not allowed."
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__char
msgid "Char"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__create_date
msgid "Created on"
msgstr "Créé le"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__data
msgid "Data"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_ir_model_fields__ttype
msgid "Field Type"
msgstr ""

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_ir_model_fields
msgid "Fields"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__float
msgid "Float"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__id
msgid "ID"
msgstr "Identifiant"

#. module: base_sparse_field
#: model:ir.model.fields,help:base_sparse_field.field_ir_model_fields__serialization_field_id
msgid "If set, this field will be stored in the sparse structure of the serialization field, instead of having its own database column. This cannot be changed after creation."
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__integer
msgid "Integer"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: base_sparse_field
#: model:ir.model.fields.selection,name:base_sparse_field.selection__sparse_fields_test__selection__one
msgid "One"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__partner
msgid "Partner"
msgstr "Partenaire"

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:0
#, python-format
msgid "Renaming sparse field \"%s\" is not allowed"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_sparse_fields_test__selection
msgid "Selection"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields,field_description:base_sparse_field.field_ir_model_fields__serialization_field_id
msgid "Serialization Field"
msgstr ""

#. module: base_sparse_field
#: code:addons/base_sparse_field/models/models.py:0
#, python-format
msgid "Serialization field %r not found for sparse field %s!"
msgstr ""

#. module: base_sparse_field
#: model:ir.model,name:base_sparse_field.model_sparse_fields_test
msgid "Sparse fields Test"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields.selection,name:base_sparse_field.selection__sparse_fields_test__selection__two
msgid "Two"
msgstr ""

#. module: base_sparse_field
#: model:ir.model.fields.selection,name:base_sparse_field.selection__ir_model_fields__ttype__serialized
msgid "serialized"
msgstr ""
