# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_automation
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"\"\n"
"                (ID:"
msgstr ""
"\"\n"
"                (ID:"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
#, python-format
msgid "\"%s\" tag is added"
msgstr "Se agregó la etiqueta \"%s\""

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"\"On live update\" automation rules can only be used with \"Execute Python "
"Code\" action type."
msgstr ""
"Las reglas de automatización \"Al actualizar en tiempo real\" solo se pueden"
" ejecutar mediante una acción de tipo \"Ejecutar código Python\"."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "%s actions"
msgstr "%s acciones"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "1 action"
msgstr "1 acción"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<code>env</code>: environment on which the action is triggered"
msgstr "<code>env</code>: entorno en el que se activa la acción."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>model</code>: model of the record on which the action is triggered; is"
" a void recordset"
msgstr ""
"<code>model</code>: modelo del registro en el cual se activa la acción. Es "
"un conjunto de registros vacío."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>payload</code>: the payload of the call (GET parameters, JSON body), "
"as a dict."
msgstr ""
"<code>payload</code>: la carga útil de la solicitud o respuesta de la "
"llamada (parámetros GET, estructura JSON) como diccionario. "

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>time</code>, <code>datetime</code>, <code>dateutil</code>, "
"<code>timezone</code>: useful Python libraries"
msgstr ""
"<code>time</code>, <code>datetime</code>, <code>dateutil</code>, "
"<code>timezone</code>: bibliotecas útiles de Python"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
msgid "<i class=\"fa fa-2x fa-arrow-right text-primary\" title=\"Actions\"/>"
msgstr "<i class=\"fa fa-2x fa-arrow-right text-primary\" title=\"Acciones\"/>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<i class=\"fa fa-info-circle\"/> The default target record getter will work "
"out-of-the-box for any webhook coming from another Odoo instance."
msgstr ""
"<i class=\"fa fa-info-circle\"/> El método getter de registros de destino "
"predeterminado funcionará al instante para cualquier webhook procedente de "
"otra instancia de Odoo."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<i class=\"fa fa-warning\"/> Automation rules triggered by UI changes will "
"be executed <em>every time</em> the watched fields change, <em>whether you "
"save or not</em>."
msgstr ""
"<i class=\"fa fa-warning\"/> Las reglas de automatización que se activan por"
" cambios en la IU se ejecutarán <em>cada que</em> que los campos observados "
"cambien, <em>sin importar si los guarda o no</em>."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span class=\"text-muted\"> Available variables: </span>"
msgstr "<span class=\"text-muted\"> Variables disponibles: </span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<span invisible=\"evaluation_type != 'value'\">to</span>\n"
"                                                                <span invisible=\"evaluation_type != 'equation'\">as</span>"
msgstr ""
"<span invisible=\"evaluation_type != 'value'\">de</span>\n"
"                                                                <span invisible=\"evaluation_type != 'equation'\">como</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<span invisible=\"trigger != 'on_time_created'\">after creation</span>\n"
"                                    <span invisible=\"trigger != 'on_time_updated'\">after last update</span>\n"
"                                    <span invisible=\"trigger != 'on_time'\">after</span>"
msgstr ""
"<span invisible=\"trigger != 'on_time_created'\">después de crear</span>\n"
"                                    <span invisible=\"trigger != 'on_time_updated'\">después de la última actualización</span>\n"
"                                    <span invisible=\"trigger != 'on_time'\">después de</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by adding</span>"
msgstr "<span>al agregar</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by clearing it</span>"
msgstr "<span>al retirar</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by removing</span>"
msgstr "<span>al eliminar</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by setting it to</span>"
msgstr "<span>al establecer a</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<strong><i class=\"fa fa-lock\"/> Keep it secret, keep it safe.</strong>"
msgstr ""
"<strong><i class=\"fa fa-lock\"/> Manténgalo en secreto y guárdelo.</strong>"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__name
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__name
msgid "Action Name"
msgstr "Nombre de la acción"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__action_server_ids
msgid "Actions"
msgstr "Acciones"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Actions To Do"
msgstr "Acciones a realizar"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__active
msgid "Active"
msgstr "Activo"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Add an action"
msgstr "Agregar una acción"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Add followers"
msgstr "Agregar seguidores"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Add followers: %(partner_names)s"
msgstr "Agregar seguidores: %(partner_names)s"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time_created
msgid "After creation"
msgstr "Después de crear"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time_updated
msgid "After last update"
msgstr "Después de la última actualización"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_domain
msgid "Apply on"
msgstr "Aplicar en"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_search
msgid "Archived"
msgstr "Archivado"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid "Automate <em>everything</em> with Automation Rules"
msgstr "Automatice <em>todo</em> con reglas de automatización"

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__base_automation_id
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__base_automation_id
#: model:ir.model.fields.selection,name:base_automation.selection__ir_actions_server__usage__base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Automation Rule"
msgstr "Regla de automatización"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__name
msgid "Automation Rule Name"
msgstr "Nombre de la regla de automatización"

#. module: base_automation
#: model:ir.actions.act_window,name:base_automation.base_automation_act
#: model:ir.ui.menu,name:base_automation.menu_base_automation_form
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_tree
msgid "Automation Rules"
msgstr "Reglas de automatización"

#. module: base_automation
#: model:ir.actions.server,name:base_automation.ir_cron_data_base_automation_check_ir_actions_server
msgid "Automation Rules: check and execute"
msgstr "Reglas de automatización: verificar y ejecutar"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
#, python-format
msgid "Automations"
msgstr "Automatizaciones"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time
msgid "Based on date field"
msgstr "Según el campo de fecha"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_pre_domain
msgid "Before Update Domain"
msgstr "Antes de actualizar el dominio"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"Change the URL's secret if you think the URL is no longer secure. You will "
"have to update any automated system that calls this webhook to the new URL."
msgstr ""
"Cambie el secreto de la URL si cree que la URL ya no es segura. Tendrá que "
"actualizar cualquier sistema automatizado que llame a este webhook a la "
"nueva URL."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Compute"
msgstr "Calcular"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Create %(model_name)s with name %(value)s"
msgstr "Crear %(model_name)s con el nombre %(value)s"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Create a new Record"
msgstr "Crear un nuevo registro"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Create activity: %(activity_name)s"
msgstr "Crear actividad: %(activity_name)s"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Create next activity"
msgstr "Crear siguiente actividad"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_date
msgid "Created on"
msgstr "Creado el"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Custom"
msgstr "Personalizado"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Date Field"
msgstr "Campo de fecha"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__day
#, python-format
msgid "Days"
msgstr "Días"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Delay"
msgstr "Espera"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_range
msgid ""
"Delay after the trigger date. You can put a negative number if you need a "
"delay before the trigger date, like sending a reminder 15 minutes before a "
"meeting."
msgstr ""
"Espera después de fecha de activación. Puede colocar un número negativo si "
"necesita un periodo de espera antes de la fecha de activación, como enviar "
"un recordatorio 15 minutos antes de una reunión."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range
msgid "Delay after trigger date"
msgstr "Retraso después de la fecha de activación"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range_type
msgid "Delay type"
msgstr "Tipo de atraso"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Delete Action"
msgstr "Eliminar acción"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Deprecated (do not use)"
msgstr "Obsoleto (no se debe utilizar)"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__description
msgid "Description"
msgstr "Descripción"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid "Disable Automation Rule"
msgstr "Desactivar regla de automatización"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"Disabling this automation rule will enable you to continue your workflow\n"
"                but any data created after this could potentially be corrupted,\n"
"                as you are effectively disabling a customization that may set\n"
"                important and/or required fields."
msgstr ""
"Deshabilitar esta acción automatizada le permitirá continuar con su flujo de trabajo\n"
"                pero cualquiera de los datos que haya creado después de esto podrían estar dañados,\n"
"                ya que está deshabilitando la personalización configurada en los \n"
"                campos importantes o requeridos"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid "Edit Automation Rule"
msgstr "Editar regla de automatización"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Email"
msgstr "Correo electrónico"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Email Events"
msgstr "Eventos de correo electrónico"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Email, follower or activity action types cannot be used when deleting "
"records, as there are no more records to apply these changes to!"
msgstr ""
"Los correos electrónicos, seguidores o tipos de acción de las actividades ya"
" no se pueden utilizar al eliminar los registros, ya que no hay más "
"registros en donde se puedan aplicar estos cambios."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Execute Python Code"
msgstr "Ejecutar código Python"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Execute several actions"
msgstr "Ejecutar varias acciones"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "External"
msgstr "Externo"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Extra Conditions"
msgstr "Condiciones adicionales"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr "Campos que desencadenan el \"onchange\"."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Generic User"
msgstr "Usuario genérico"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_is_mail_thread
msgid "Has Mail Thread"
msgstr "Tiene un hilo de correos"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__hour
msgid "Hours"
msgstr "Horas"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__id
msgid "ID"
msgstr "ID"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the automation"
" rule."
msgstr ""
"Si está marcada esta casilla, la condición debe cumplirse antes de ejecutar "
"la regla de automatización."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record. Not checked on record creation."
msgstr ""
"Si la casilla está seleccionada, entonces es necesario cumplir esta "
"condición antes de actualizar el registro. No está seleccionada al crear un "
"registro."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Keep track of what this automation does and why it exists..."
msgstr "Lleve registro de lo que hace esta automatización y por qué existe..."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__last_run
msgid "Last Run"
msgstr "Última ejecución"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__least_delay_msg
msgid "Least Delay Msg"
msgstr "Mínimo de tiempo de espera del mensaje"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__log_webhook_calls
msgid "Log Calls"
msgstr "Registrar llamadas"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Logs"
msgstr "Registros"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Mail event can not be configured on model %s. Only models with discussion "
"feature can be used."
msgstr ""
"No puede configurar un evento de correo electrónico en el modelo %s, solo "
"puede utilizar los modelos con las funciones de Conversaciones."

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__minutes
msgid "Minutes"
msgstr "Minutos"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_id
msgid "Model"
msgstr "Modelo"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_name
msgid "Model Name"
msgstr "Nombre del modelo"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid ""
"Model of action %(action_name)s should match the one from automated rule "
"%(rule_name)s."
msgstr ""
"El modelo de la acción %(action_name)s debe coincidir con el modelo de la "
"regla automatizada %(rule_name)s."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__model_id
msgid "Model on which the automation rule runs."
msgstr "Modelo sobre el que se ejecuta la regla de automatización."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__month
#, python-format
msgid "Months"
msgstr "Meses"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "No record to run the automation on was found."
msgstr "No se encontró ningún registro para ejecutar la automatización."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Note that this automation rule can be triggered up to %d minutes after its "
"schedule."
msgstr ""
"Tome en cuenta que esta regla de automatización se puede activar hasta %d "
"minutos después de lo programado."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Notes"
msgstr "Notas"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "Activar al modificar campos"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_change
msgid "On UI change"
msgstr "Al cambio de la IU"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_archive
msgid "On archived"
msgstr "Al archivar"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create
msgid "On creation"
msgstr "Al crear"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unlink
msgid "On deletion"
msgstr "Al eliminar"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_message_received
msgid "On incoming message"
msgstr "Al recibir un mensaje"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_message_sent
msgid "On outgoing message"
msgstr "Al enviar un mensaje"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create_or_write
msgid "On save"
msgstr "Al guardar"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unarchive
msgid "On unarchived"
msgstr "Al desarchivar"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_write
msgid "On update"
msgstr "Al actualizar"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_webhook
msgid "On webhook"
msgstr "Al usar webhook"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Post as Message"
msgstr "Publicar como mensaje"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Post as Note"
msgstr "Publicar como nota"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_priority_set
msgid "Priority is set to"
msgstr "La prioridad está establecida en"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__record_getter
msgid "Record Getter"
msgstr "Método getter de registros"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Remove followers"
msgstr "Eliminar seguidores"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Remove followers: %(partner_names)s"
msgstr "Eliminar seguidores: %(partner_names)s"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Rotate Secret"
msgstr "Rotar secreto"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select a date field..."
msgstr "Seleccione un campo de fecha..."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select a value..."
msgstr "Seleccione un valor..."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select fields..."
msgstr "Seleccionar campos..."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Send SMS Text Message"
msgstr "Enviar mensaje de texto SMS"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Send SMS: %(template_name)s"
msgstr "Enviar mensaje SMS: %(template_name)s"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Send Webhook Notification"
msgstr "Enviar notificación webhook"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"Send an email when an object changes state, archive records\n"
"                after a month of inactivity or remind yourself to follow-up on\n"
"                tasks when a specific tag is added."
msgstr ""
"Envíe un correo electrónico cuando un objeto cambie de estado, archive los registros \n"
"                después de un mes de inactividad o establezca un recordatorio para realizar seguimiento \n"
"                de las tareas al agregar una etiqueta específica."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Send email"
msgstr "Enviar correo"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Send email: %(template_name)s"
msgstr "Enviar correo electrónico: %(template_name)s"

#. module: base_automation
#: model:ir.model,name:base_automation.model_ir_actions_server
msgid "Server Action"
msgstr "Acción del servidor"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_selection_field_id
msgid ""
"Some triggers need a reference to a selection field. This field is used to "
"store it."
msgstr ""
"Algunos activadores necesitan una referencia a un campo de selección, este "
"campo se usa para almacenarlo."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_field_ref
msgid ""
"Some triggers need a reference to another field. This field is used to store"
" it."
msgstr ""
"Algunos activadores necesitan una referencia a otro campo, este campo se usa"
" para almacenarlo."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Specific User"
msgstr "Usuario específico"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_stage_set
msgid "Stage is set to"
msgstr "La etapa está establecida como"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
#, python-format
msgid "Stage is set to \"%s\""
msgstr "La etapa está establecida como \"%s\""

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_state_set
msgid "State is set to"
msgstr "El estado está establecido como"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_tag_set
msgid "Tag is added"
msgstr "Se agregó la etiqueta"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Target Record"
msgstr "Registro objetivo"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Target model of actions %(action_names)s are different from rule model."
msgstr ""
"El modelo objetivo de las acciones %(action_names)s es distinto al modelo de"
" la regla."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"The \"%(trigger_value)s\" %(trigger_label)s can only be used with the "
"\"%(state_value)s\" action type"
msgstr ""
"El \"%(trigger_value)s\" %(trigger_label)s solo se puede utilizar con el "
"tipo de acción \"%(state_value)s\""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trigger_field_ids
msgid ""
"The automation rule will be triggered if and only if one of these fields is "
"updated.If empty, all fields are watched."
msgstr ""
"La regla de automatización se activará si y solo si uno de estos campos se "
"actualiza. Si está vacía, se observan todos los campos."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"The error occurred during the execution of the automation rule\n"
"                \""
msgstr ""
"Ocurrió un error al ejecutar la regla de automatización \n"
"                \""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__record_getter
msgid ""
"This code will be run to find on which record the automation rule should be "
"run."
msgstr ""
"Este código se ejecutará para encontrar en qué registro se debe ejecutar la "
"regla de automatización."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Timing Conditions"
msgstr "Condiciones de tiempo"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger
msgid "Trigger"
msgstr "Activar"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_id
msgid "Trigger Date"
msgstr "Fecha de activación"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_selection_field_id
msgid "Trigger Field"
msgstr "Campo de activación"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref_model_name
msgid "Trigger Field Model"
msgstr "Modelo del campo de activación"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger_field_ids
msgid "Trigger Fields"
msgstr "Campos de activación"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref
msgid "Trigger Reference"
msgstr "Referencia de activación"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref_display_name
msgid "Trigger Reference Display Name"
msgstr "Nombre en pantalla de la referencia para activar"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.ir_actions_server_view_form_automation
msgid "Type"
msgstr "Tipo"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "URL"
msgstr "URL"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "URL will be created once the rule is saved."
msgstr "La URL se creará una vez que guarde la regla."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Update"
msgstr "Actualizar"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Update the Record"
msgstr "Actualizar el registro"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__url
msgid "Url"
msgstr "URL"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__usage
msgid "Usage"
msgstr "Uso"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_calendar_id
msgid "Use Calendar"
msgstr "Usar calendario"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_user_set
msgid "User is set"
msgstr "Se estableció el usuario"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Values Updated"
msgstr "Valores actualizados"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Warning"
msgstr "Advertencia"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Webhook Log"
msgstr "Registro del webhook"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Webhook Logs"
msgstr "Registros del webhook"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__webhook_uuid
msgid "Webhook UUID"
msgstr "UUID del webhook"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Weeks"
msgstr "Semanas"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possibleto use a "
"calendar to compute the date based on working days."
msgstr ""
"Cuando se calcula una condición de tiempo basada en días, es posible "
"utilizar un calendario para calcular la fecha según los días laborables."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""
"Cuando se debe activar la condición.\n"
"                Si se encuentra seleccionada, la revisará el programador. De lo contrario, se revisará en la creación y actualización."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr ""
"Si no se encuentra seleccionada, la regla está oculta y no se ejecutará."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "When updating"
msgstr "Al actualizar"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"With Automation Rules, you can automate\n"
"                <em>any</em> workflow."
msgstr ""
"Con las reglas de automatización puede automatizar\n"
"                <em>cualquier</em> flujo de trabajo."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"You can ask an administrator to disable or correct this automation rule."
msgstr ""
"Puede solicitarle a un administrador que desactive o corrija esta regla de "
"automatización."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid "You can disable this automation rule or edit it to solve the issue."
msgstr ""
"Puede deshabilitar esta regla de automatización o editarla para resolver el "
"problema."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"You cannot send an email, add followers or create an activity for a deleted "
"record.  It simply does not work."
msgstr ""
"No puede enviar un correo, agregar seguidores o crear una actividad para un "
"registro eliminado."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"Your webhook URL contains a secret. Don't share it online or carelessly."
msgstr ""
"La URL del webhook contiene un secreto, no lo comparta en ningún lado."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
msgid "based on"
msgstr "según"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "e.g. Support flow"
msgstr "Por ejemplo, flujo de soporte"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.xml:0
#, python-format
msgid "no action defined..."
msgstr "no hay ninguna acción definida..."
