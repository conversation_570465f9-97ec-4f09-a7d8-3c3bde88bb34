# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_automation
# 
# Translators:
# Ped, 2023
# Wil <PERSON>doo, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Rasareeyar Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"\"\n"
"                (ID:"
msgstr ""
"\"\n"
"                (ID:"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
#, python-format
msgid "\"%s\" tag is added"
msgstr "เพิ่มแท็ก \"%s\" แล้ว"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"\"On live update\" automation rules can only be used with \"Execute Python "
"Code\" action type."
msgstr ""
"กฎอัตโนมัติ \"ในการอัปเดตแบบไลฟ์\" สามารถใช้ได้เฉพาะกับประเภทการดำเนินการ "
"\"เรียกใช้โค้ด Python\" เท่านั้น"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "%s actions"
msgstr "การดำเนินการ %s"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "1 action"
msgstr "1 การดำเนินการ"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<code>env</code>: environment on which the action is triggered"
msgstr "<code>env</code>: สภาพแวดล้อมที่การดำเนินการถูกทริกเกอร์"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>model</code>: model of the record on which the action is triggered; is"
" a void recordset"
msgstr ""
"<code>model</code>: แบบจำลองของเรกคอร์ดที่ทริกเกอร์การดำเนินการ "
"เป็นชุดบันทึกที่ว่างเปล่า"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>payload</code>: the payload of the call (GET parameters, JSON body), "
"as a dict."
msgstr ""
"<code>เพย์โหลด</code>: เพย์โหลดของการโทร (พารามิเตอร์ GET, เนื้อความ JSON) "
"เป็นคำสั่ง"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>time</code>, <code>datetime</code>, <code>dateutil</code>, "
"<code>timezone</code>: useful Python libraries"
msgstr ""
"<code>เวลา</code>, <code>วันที่เวลา</code>, <code>วันที่จนถึง</code>, "
"<code>เขตเวลา</code>: ไลบรารี Python ที่มีประโยชน์"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
msgid "<i class=\"fa fa-2x fa-arrow-right text-primary\" title=\"Actions\"/>"
msgstr "<i class=\"fa fa-2x fa-arrow-right text-primary\" title=\"การดำเนินการ\"/>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<i class=\"fa fa-info-circle\"/> The default target record getter will work "
"out-of-the-box for any webhook coming from another Odoo instance."
msgstr ""
"<i class=\"fa fa-info-circle\"/> "
"ตัวรับบันทึกเป้าหมายเริ่มต้นจะทำงานได้ทันทีสำหรับเว็บฮุคที่มาจากอินสแตนซ์อื่นของ"
" Odoo"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<i class=\"fa fa-warning\"/> Automation rules triggered by UI changes will "
"be executed <em>every time</em> the watched fields change, <em>whether you "
"save or not</em>."
msgstr ""
"<i class=\"fa fa-warning\"/>กฎการทำงานอัตโนมัติที่ทริกเกอร์โดยการเปลี่ยนแปลง"
" UI จะถูกดำเนินการ<em>ทุกครั้ง</em>ที่มีการเปลี่ยนแปลงฟิลด์ที่เฝ้าดู "
"<em>ไม่ว่าคุณจะบันทึกหรือไม่ก็ตาม</em>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span class=\"text-muted\"> Available variables: </span>"
msgstr "<span class=\"text-muted\"> ตัวแปรที่มีอยู่: </span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<span invisible=\"evaluation_type != 'value'\">to</span>\n"
"                                                                <span invisible=\"evaluation_type != 'equation'\">as</span>"
msgstr ""
"<span invisible=\"evaluation_type != 'value'\">ถึง</span>\n"
"                                                                <span invisible=\"evaluation_type != 'equation'\">เป็น</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<span invisible=\"trigger != 'on_time_created'\">after creation</span>\n"
"                                    <span invisible=\"trigger != 'on_time_updated'\">after last update</span>\n"
"                                    <span invisible=\"trigger != 'on_time'\">after</span>"
msgstr ""
"<span invisible=\"trigger != 'on_time_created'\">หลังจากการสร้าง</span>\n"
"                                    <span invisible=\"trigger != 'on_time_updated'\">หลังจากการอัพเดตครั้งล่าสุด</span>\n"
"                                    <span invisible=\"trigger != 'on_time'\">หลังจาก</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by adding</span>"
msgstr "<span>โดยการเพิ่ม</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by clearing it</span>"
msgstr "<span>โดยการล้าง</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by removing</span>"
msgstr "<span>โดยการถอด</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by setting it to</span>"
msgstr "<span>โดยตั้งค่าเป็น</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<strong><i class=\"fa fa-lock\"/> Keep it secret, keep it safe.</strong>"
msgstr ""
"<strong><i class=\"fa fa-lock\"/> เก็บมันไว้เป็นความลับ "
"เพื่อความปลอดภัย</strong>"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__name
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__name
msgid "Action Name"
msgstr "ชื่อการดำเนินการ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__action_server_ids
msgid "Actions"
msgstr "การดำเนินการ"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Actions To Do"
msgstr "การกระทำที่ต้องทำ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Add an action"
msgstr "เพิ่มการกระทำ"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Add followers"
msgstr "เพิ่มผู้ติดตาม"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Add followers: %(partner_names)s"
msgstr "เพิ่มผู้ติดตาม: %(partner_names)s"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time_created
msgid "After creation"
msgstr "หลังจากการสร้าง"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time_updated
msgid "After last update"
msgstr "หลังจากอัปเดตครั้งล่าสุด"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_domain
msgid "Apply on"
msgstr "ใช้เมื่อ"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_search
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid "Automate <em>everything</em> with Automation Rules"
msgstr "ทำให้<em>ทุกอย่าง</em>เป็นอัตโนมัติด้วยกฎการทำงานอัตโนมัติ"

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__base_automation_id
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__base_automation_id
#: model:ir.model.fields.selection,name:base_automation.selection__ir_actions_server__usage__base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Automation Rule"
msgstr "กฎอัตโนมัติ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__name
msgid "Automation Rule Name"
msgstr "ชื่อกฎการทำงานอัตโนมัติ"

#. module: base_automation
#: model:ir.actions.act_window,name:base_automation.base_automation_act
#: model:ir.ui.menu,name:base_automation.menu_base_automation_form
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_tree
msgid "Automation Rules"
msgstr "กฎการทำงานอัตโนมัติ"

#. module: base_automation
#: model:ir.actions.server,name:base_automation.ir_cron_data_base_automation_check_ir_actions_server
msgid "Automation Rules: check and execute"
msgstr "กฎการทำงานอัตโนมัติ: ตรวจสอบและดำเนินการ"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
#, python-format
msgid "Automations"
msgstr "ระบบอัตโนมัติ"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time
msgid "Based on date field"
msgstr "ขึ้นอยู่กับฟิลด์วันที่"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_pre_domain
msgid "Before Update Domain"
msgstr "ก่อนอัปเดตโดเมน"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"Change the URL's secret if you think the URL is no longer secure. You will "
"have to update any automated system that calls this webhook to the new URL."
msgstr ""
"เปลี่ยนข้อมูลลับของ URL หากคุณคิดว่า URL นั้นไม่ปลอดภัยอีกต่อไป "
"คุณจะต้องอัปเดตระบบอัตโนมัติที่เรียกเว็บฮุคนี้เป็น URL ใหม่"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Compute"
msgstr "คำนวณ"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Create %(model_name)s with name %(value)s"
msgstr "สร้าง %(model_name)s ด้วยชื่อ %(value)s"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Create a new Record"
msgstr "สร้างรายการใหม่"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Create activity: %(activity_name)s"
msgstr "สร้างกิจกรรม: %(activity_name)s"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Create next activity"
msgstr "สร้างกิจกรรมถัดไป"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Custom"
msgstr "กำหนดเอง"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Date Field"
msgstr "ฟิลด์วันที่"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__day
#, python-format
msgid "Days"
msgstr "วัน"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Delay"
msgstr "ล่าช้า"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_range
msgid ""
"Delay after the trigger date. You can put a negative number if you need a "
"delay before the trigger date, like sending a reminder 15 minutes before a "
"meeting."
msgstr ""
"ความล่าช้าหลังจากวันที่เปิดใช้งาน "
"คุณสามารถใส่ตัวเลขติดลบได้หากต้องการให้เกิดความล่าช้าก่อนวันที่เปิดใช้งาน "
"เช่น การส่งการแจ้งเตือน 15 นาทีก่อนการประชุม"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range
msgid "Delay after trigger date"
msgstr "ความล่าช้าหลังจากวันเปิดใช้งาน"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range_type
msgid "Delay type"
msgstr "ประเภทการล่าช้า"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Delete Action"
msgstr "ลบการกระทำ"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Deprecated (do not use)"
msgstr "เลิกใช้แล้ว (อย่าใช้)"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__description
msgid "Description"
msgstr "คำอธิบาย"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid "Disable Automation Rule"
msgstr "ปิดใช้งานกฎการทำงานอัตโนมัติ"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"Disabling this automation rule will enable you to continue your workflow\n"
"                but any data created after this could potentially be corrupted,\n"
"                as you are effectively disabling a customization that may set\n"
"                important and/or required fields."
msgstr ""
"การปิดใช้งานกฎอัตโนมัตินี้จะช่วยให้คุณสามารถดำเนินการเวิร์กโฟลว์ต่อไปได้ \n"
"              แต่ข้อมูลใดที่สร้างขึ้นหลังจากนั้นอาจเสียหายได้\n"
"              เนื่องจากคุณกำลังปิดใช้งานการปรับแต่งที่อาจตั้งค่า\n"
"              ฟิลด์สำคัญและ/หรือจำเป็นอย่างมีประสิทธิภาพ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid "Edit Automation Rule"
msgstr "แก้ไขกฎการทำงานอัตโนมัติ"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Email"
msgstr "อีเมล"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Email Events"
msgstr "กิจกรรมอีเมล"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Email, follower or activity action types cannot be used when deleting "
"records, as there are no more records to apply these changes to!"
msgstr ""
"ไม่สามารถใช้ประเภทการดำเนินการของอีเมล ผู้ติดตาม หรือกิจกรรมเมื่อลบบันทึก "
"เนื่องจากไม่มีบันทึกอีกต่อไปที่จะใช้การเปลี่ยนแปลงเหล่านี้!"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Execute Python Code"
msgstr "รหัส Python"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Execute several actions"
msgstr "ดำเนินการหลายอย่าง"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "External"
msgstr "ภายนอก"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Extra Conditions"
msgstr "เงื่อนไขพิเศษ"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr "ฟิลด์ที่ทริกเกอร์การเปลี่ยนแปลง"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Generic User"
msgstr "ผู้ใช้ทั่วไป"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_is_mail_thread
msgid "Has Mail Thread"
msgstr "มีอีเมลเธรด"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__hour
msgid "Hours"
msgstr "ชั่วโมง"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__id
msgid "ID"
msgstr "ไอดี"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the automation"
" rule."
msgstr "หากมี ต้องปฏิบัติตามเงื่อนไขนี้ก่อนที่จะดำเนินการกฎอัตโนมัติ"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record. Not checked on record creation."
msgstr ""
"หากมี เงื่อนไขนี้ต้องเป็นไปตามก่อนอัปเดตบันทึก ไม่ได้ตรวจสอบเมื่อสร้างบันทึก"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Keep track of what this automation does and why it exists..."
msgstr "ติดตามว่าระบบอัตโนมัตินี้ทำอะไรและเพราะอะไรถึงมีอยู่..."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__last_run
msgid "Last Run"
msgstr "รันครั้งสุดท้าย"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__least_delay_msg
msgid "Least Delay Msg"
msgstr "ข้อความที่เกี่ยวกับความล่าช้าน้อยที่สุด"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__log_webhook_calls
msgid "Log Calls"
msgstr "บันทึกการโทร"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Logs"
msgstr "บันทึก"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Mail event can not be configured on model %s. Only models with discussion "
"feature can be used."
msgstr ""
"ไม่สามารถกำหนดค่าเหตุการณ์เมลในโมเดล %s ได้ "
"สามารถใช้ได้เฉพาะโมเดลที่มีฟีเจอร์ของแชทเท่านั้น"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__minutes
msgid "Minutes"
msgstr "นาที"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_id
msgid "Model"
msgstr "โมเดล"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_name
msgid "Model Name"
msgstr "ชื่อโมเดล"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid ""
"Model of action %(action_name)s should match the one from automated rule "
"%(rule_name)s."
msgstr ""
"รูปแบบการกระทำ %(action_name)s ควรตรงกับรูปแบบจากกฎอัตโนมัติ %(rule_name)s"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__model_id
msgid "Model on which the automation rule runs."
msgstr "แบบจำลองที่กฎการทำงานอัตโนมัติทำงาน"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__month
#, python-format
msgid "Months"
msgstr "เดือน"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "No record to run the automation on was found."
msgstr "ไม่พบบันทึกที่จะเรียกใช้ระบบอัตโนมัติ"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Note that this automation rule can be triggered up to %d minutes after its "
"schedule."
msgstr ""
"โปรดทราบว่ากฎอัตโนมัตินี้สามารถทริกเกอร์ได้สูงสุด %d นาที หลังจากกำหนดเวลา"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Notes"
msgstr "โน้ต"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "เมื่อเปลี่ยนทริกเกอร์ฟิลด์"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_change
msgid "On UI change"
msgstr "เมื่อเปลี่ยน UI"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_archive
msgid "On archived"
msgstr "เมื่อเก็บถาวร"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create
msgid "On creation"
msgstr "เมื่อมีการสร้าง"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unlink
msgid "On deletion"
msgstr "เมื่อมีการลบ"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_message_received
msgid "On incoming message"
msgstr "เมื่อมีข้อความขาเข้า"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_message_sent
msgid "On outgoing message"
msgstr "เมื่อมีข้อความขาออก"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create_or_write
msgid "On save"
msgstr "เมื่อบันทึก"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unarchive
msgid "On unarchived"
msgstr "เมื่อยกเลิกการเก็บถาวร"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_write
msgid "On update"
msgstr "เมื่อมีการอัปเดต"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_webhook
msgid "On webhook"
msgstr "บนเว็บฮุค"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Post as Message"
msgstr "โพสต์เป็นข้อความ"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Post as Note"
msgstr "โพสต์เป็นโน๊ต"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_priority_set
msgid "Priority is set to"
msgstr "ลำดับความสำคัญถูกตั้งค่าเป็น"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__record_getter
msgid "Record Getter"
msgstr "บันทึก Getter"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Remove followers"
msgstr "ลบผู้ติดตาม"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Remove followers: %(partner_names)s"
msgstr "ลบผู้ติดตาม: %(partner_names)s"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Rotate Secret"
msgstr "หมุนข้อมูลลับ"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select a date field..."
msgstr "เลือกช่องวันที่..."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select a value..."
msgstr "เลือกค่า..."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select fields..."
msgstr "เลือกฟิลด์..."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Send SMS Text Message"
msgstr "ส่งข้อความ SMS"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Send SMS: %(template_name)s"
msgstr "ส่ง SMS: %(template_name)s"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Send Webhook Notification"
msgstr "ส่งการแจ้งเตือน Webhook"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"Send an email when an object changes state, archive records\n"
"                after a month of inactivity or remind yourself to follow-up on\n"
"                tasks when a specific tag is added."
msgstr ""
"ส่งอีเมลเมื่อออบเจ็กต์เปลี่ยนสถานะ เก็บถาวรบันทึก\n"
"              หลังจากไม่มีการใช้งานเป็นเวลาหนึ่งเดือน หรือเตือนตัวเองให้ติดตาม\n"
"              งานเมื่อมีการเพิ่มแท็กเฉพาะ"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Send email"
msgstr "ส่งอีเมล"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Send email: %(template_name)s"
msgstr "ส่งอีเมล: %(template_name)s"

#. module: base_automation
#: model:ir.model,name:base_automation.model_ir_actions_server
msgid "Server Action"
msgstr "การดำเนินการเซิร์ฟเวอร์"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_selection_field_id
msgid ""
"Some triggers need a reference to a selection field. This field is used to "
"store it."
msgstr ""
"การเปิดใช้งานบางตัวจำเป็นต้องมีการอ้างอิงไปยังช่องการเลือก "
"ช่องนี้ใช้เพื่อจัดเก็บ"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_field_ref
msgid ""
"Some triggers need a reference to another field. This field is used to store"
" it."
msgstr ""
"การเปิดใช้งานบางตัวจำเป็นต้องมีการอ้างอิงไปยังช่องอื่น "
"ช่องนี้ใช้เพื่อจัดเก็บ"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Specific User"
msgstr "ผู้ใช้เฉพาะ"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_stage_set
msgid "Stage is set to"
msgstr "ขั้นตอนถูกตั้งค่าเป็น"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
#, python-format
msgid "Stage is set to \"%s\""
msgstr "ขั้นตอนถูกตั้งค่าเป็น \"%s\""

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_state_set
msgid "State is set to"
msgstr "ขั้นตอนถูกตั้งค่าเป็น"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_tag_set
msgid "Tag is added"
msgstr "เพิ่มแท็กแล้ว"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Target Record"
msgstr "บันทึกเป้าหมาย"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Target model of actions %(action_names)s are different from rule model."
msgstr "รูปแบบเป้าหมายของการกระทำ %(action_names)s แตกต่างจากรูปแบบกฎ"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"The \"%(trigger_value)s\" %(trigger_label)s can only be used with the "
"\"%(state_value)s\" action type"
msgstr ""
"\"%(trigger_value)s\" %(trigger_label)s ใช้ได้กับประเภทการกระทำ "
"\"%(state_value)s\" เท่านั้น"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trigger_field_ids
msgid ""
"The automation rule will be triggered if and only if one of these fields is "
"updated.If empty, all fields are watched."
msgstr ""
"กฎอัตโนมัติจะถูกเปิดใช้งานหากมีการอัปเดตช่องใดช่องหนึ่งเหล่านี้ หากว่างเปล่า"
" ระบบจะเฝ้าดูช่องทั้งหมด"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"The error occurred during the execution of the automation rule\n"
"                \""
msgstr ""
"เกิดข้อผิดพลาดระหว่างการดำเนินการกฎอัตโนมัติ\n"
"                \""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__record_getter
msgid ""
"This code will be run to find on which record the automation rule should be "
"run."
msgstr "รหัสนี้จะถูกเรียกใช้เพื่อค้นหาว่าควรรันบันทึกใดที่กฎการทำงานอัตโนมัติ"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Timing Conditions"
msgstr "เงื่อนไขการกำหนดเวลา"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger
msgid "Trigger"
msgstr "เปิดใช้งาน"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_id
msgid "Trigger Date"
msgstr "วันที่เปิดใช้งาน"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_selection_field_id
msgid "Trigger Field"
msgstr "การเปิดการใช้งานฟิลด์"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref_model_name
msgid "Trigger Field Model"
msgstr "การเปิดใช้งานแบบจำลองฟิลด์"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger_field_ids
msgid "Trigger Fields"
msgstr "ทริกเกอร์ฟิลด์"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref
msgid "Trigger Reference"
msgstr "การอ้างอิงการเปิดใช้งานา"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref_display_name
msgid "Trigger Reference Display Name"
msgstr "ชื่อที่แสดงการอ้างอิงการเปิดใช้งาน"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.ir_actions_server_view_form_automation
msgid "Type"
msgstr "ประเภท"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "URL"
msgstr "URL"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "URL will be created once the rule is saved."
msgstr "URL จะถูกสร้างขึ้นเมื่อบันทึกกฎแล้ว"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Update"
msgstr "อัปเดต"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Update the Record"
msgstr "อัพเดทรายงาน"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__url
msgid "Url"
msgstr "Url"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__usage
msgid "Usage"
msgstr "การใช้งาน"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_calendar_id
msgid "Use Calendar"
msgstr "ใช้ปฏิทิน"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_user_set
msgid "User is set"
msgstr "ผู้ใช้ถูกตั้งค่าแล้ว"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Values Updated"
msgstr "อัปเดตค่าแล้ว"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Warning"
msgstr "คำเตือน"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Webhook Log"
msgstr "บันทึก Webhook"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Webhook Logs"
msgstr "บันทึก Webhook"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__webhook_uuid
msgid "Webhook UUID"
msgstr "Webhook UUID"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Weeks"
msgstr "สัปดาห์"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possibleto use a "
"calendar to compute the date based on working days."
msgstr ""
"เมื่อคำนวณเงื่อนไขการกำหนดเวลาตามวัน "
"คุณสามารถใช้ปฏิทินเพื่อคำนวณวันที่ตามวันทำการได้"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""
"เมื่อใดที่ควรทริกเกอร์เงื่อนไข\n"
"ถ้ามีจะมีการตรวจสอบโดยผู้จัดตารางเวลา ถ้าหากว่างเปล่า จะถูกตรวจสอบเมื่อถูกสร้างและอัปเดต"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr "เมื่อยกเลิกการเลือก กฎจะถูกซ่อนและจะไม่ถูกดำเนินการ"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "When updating"
msgstr "เมื่อทำการอัปเดต"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"With Automation Rules, you can automate\n"
"                <em>any</em> workflow."
msgstr ""
"ด้วยกฎการทำงานอัตโนมัติ คุณสามารถทำให้เป็นอัตโนมัติได้\n"
"                ขั้นตอนการทำงาน<em>ใดๆ</em>"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"You can ask an administrator to disable or correct this automation rule."
msgstr "คุณสามารถขอให้ผู้ดูแลระบบปิดใช้งานหรือแก้ไขกฎอัตโนมัตินี้ได้"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid "You can disable this automation rule or edit it to solve the issue."
msgstr "คุณสามารถปิดใช้งานกฎการทำงานอัตโนมัตินี้หรือแก้ไขเพื่อแก้ไขปัญหาได้"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"You cannot send an email, add followers or create an activity for a deleted "
"record.  It simply does not work."
msgstr ""
"คุณไม่สามารถส่งอีเมล เพิ่มผู้ติดตาม หรือสร้างกิจกรรมสำหรับบันทึกที่ถูกลบได้ "
"เพราะว่ามันไม่สามารถทำได้"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"Your webhook URL contains a secret. Don't share it online or carelessly."
msgstr ""
"URL เว็บฮุคของคุณมีข้อมูลลับ อย่าแชร์ทางออนไลน์หรือดำเนินการโดยไม่ระมัดระวัง"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
msgid "based on"
msgstr "ขึ้นอยู่กับ"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "e.g. Support flow"
msgstr "เช่น การรองรับโฟลว์"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.xml:0
#, python-format
msgid "no action defined..."
msgstr "ไม่มีการกำหนดการกระทำ..."
