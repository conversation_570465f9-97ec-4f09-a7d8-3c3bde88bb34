# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_automation
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Sarah Park, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"\"\n"
"                (ID:"
msgstr ""
"\"\n"
"                (ID:"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
#, python-format
msgid "\"%s\" tag is added"
msgstr "\"%s\" 태그가 추가되었습니다."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"\"On live update\" automation rules can only be used with \"Execute Python "
"Code\" action type."
msgstr "\"실시간 업데이트 시\" 자동화 규칙은 \"파이썬 코드 실행\" 작업 유형과 함께 사용할 수 있습니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "%s actions"
msgstr "%s 작업"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "1 action"
msgstr "1 작업"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<code>env</code>: environment on which the action is triggered"
msgstr "<code>env</code>: 동작이 트리거되는 환경"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>model</code>: model of the record on which the action is triggered; is"
" a void recordset"
msgstr "<code>model</code>: 작업이 트리거되는 레코드의 모델이며, 내용이 없는 레코드 집합입니다."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>payload</code>: the payload of the call (GET parameters, JSON body), "
"as a dict."
msgstr "<code>페이로드</code>: 호출의 페이로드 (GET 매개변수, JSON 본문)를 딕셔너리로 제공합니다."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<code>time</code>, <code>datetime</code>, <code>dateutil</code>, "
"<code>timezone</code>: useful Python libraries"
msgstr ""
"<code>time</code>, <code>datetime</code>, <code>dateutil</code>, "
"<code>timezone</code>: 유용한 파이썬 라이브러리"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
msgid "<i class=\"fa fa-2x fa-arrow-right text-primary\" title=\"Actions\"/>"
msgstr "<i class=\"fa fa-2x fa-arrow-right text-primary\" title=\"작업\"/>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<i class=\"fa fa-info-circle\"/> The default target record getter will work "
"out-of-the-box for any webhook coming from another Odoo instance."
msgstr ""
"<i class=\"fa fa-info-circle\"/> 기본 대상 레코드 가져오기는 다른 Odoo 인스턴스에서 오는 모든 웹후크에 "
"대해 즉시 작동합니다."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<i class=\"fa fa-warning\"/> Automation rules triggered by UI changes will "
"be executed <em>every time</em> the watched fields change, <em>whether you "
"save or not</em>."
msgstr ""
"<i class=\"fa fa-warning\"/> UI 변경에 의해 트리거되는 자동화 규칙은 감시 필드가 <em>변경될 "
"때마다</em>, <em>저장 여부에 관계없이</em> 실행됩니다."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span class=\"text-muted\"> Available variables: </span>"
msgstr "<span class=\"text-muted\"> 사용 가능한 변수: </span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<span invisible=\"evaluation_type != 'value'\">to</span>\n"
"                                                                <span invisible=\"evaluation_type != 'equation'\">as</span>"
msgstr ""
"<span invisible=\"evaluation_type != 'value'\">까지</span>\n"
"                                                                <span invisible=\"evaluation_type != 'equation'\">으로</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"<span invisible=\"trigger != 'on_time_created'\">after creation</span>\n"
"                                    <span invisible=\"trigger != 'on_time_updated'\">after last update</span>\n"
"                                    <span invisible=\"trigger != 'on_time'\">after</span>"
msgstr ""
"<span invisible=\"trigger != 'on_time_created'\">생성 후</span>\n"
"                                    <span invisible=\"trigger != 'on_time_updated'\">최근 업데이트 후</span>\n"
"                                    <span invisible=\"trigger != 'on_time'\">후</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by adding</span>"
msgstr "<span>추가하여</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by clearing it</span>"
msgstr "<span>제거하여</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by removing</span>"
msgstr "<span>삭제하여</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<span>by setting it to</span>"
msgstr "<span>설정하여</span>"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "<strong><i class=\"fa fa-lock\"/> Keep it secret, keep it safe.</strong>"
msgstr "<strong><i class=\"fa fa-lock\"/> 비밀 및 보안 유지.</strong>"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__name
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__name
msgid "Action Name"
msgstr "작업명"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__action_server_ids
msgid "Actions"
msgstr "활동"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Actions To Do"
msgstr "수행할 작업"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__active
msgid "Active"
msgstr "활성화"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Add an action"
msgstr "작업 추가"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Add followers"
msgstr "팔로워 추가"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Add followers: %(partner_names)s"
msgstr "팔로워 추가: %(partner_names)s"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time_created
msgid "After creation"
msgstr "생성한 후"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time_updated
msgid "After last update"
msgstr "최근 업데이트 후"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_domain
msgid "Apply on"
msgstr "적용"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_search
msgid "Archived"
msgstr "보관됨"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid "Automate <em>everything</em> with Automation Rules"
msgstr "자동화 규칙으로 <em>모든 것</em>을 자동화합니다."

#. module: base_automation
#: model:ir.model,name:base_automation.model_base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__base_automation_id
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__base_automation_id
#: model:ir.model.fields.selection,name:base_automation.selection__ir_actions_server__usage__base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Automation Rule"
msgstr "자동화 규칙"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__name
msgid "Automation Rule Name"
msgstr "자동화 규칙 이름"

#. module: base_automation
#: model:ir.actions.act_window,name:base_automation.base_automation_act
#: model:ir.ui.menu,name:base_automation.menu_base_automation_form
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_tree
msgid "Automation Rules"
msgstr "자동화 규칙"

#. module: base_automation
#: model:ir.actions.server,name:base_automation.ir_cron_data_base_automation_check_ir_actions_server
msgid "Automation Rules: check and execute"
msgstr "자동화 규칙: 확인 및 실행"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
#, python-format
msgid "Automations"
msgstr "자동"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_time
msgid "Based on date field"
msgstr "날짜 필드 기준"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__filter_pre_domain
msgid "Before Update Domain"
msgstr "도메인 업데이트 전"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"Change the URL's secret if you think the URL is no longer secure. You will "
"have to update any automated system that calls this webhook to the new URL."
msgstr ""
"URL이 유출된 것으로 의심되는 경우 비밀키를 수정하세요. 이 웹훅을 사용하는 모든 자동화 시스템을 새 URL로 업데이트하는 것을 잊지 "
"마세요."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Compute"
msgstr "계산"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Create %(model_name)s with name %(value)s"
msgstr "%(model_name)s을 %(value)s이라는 이름으로 생성합니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Create a new Record"
msgstr "새 레코드 만들기"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Create activity: %(activity_name)s"
msgstr "활동 생성: %(activity_name)s"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Create next activity"
msgstr "다음 활동 만들기"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_uid
msgid "Created by"
msgstr "작성자"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__create_date
msgid "Created on"
msgstr "작성일자"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Custom"
msgstr "사용자 지정"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Date Field"
msgstr "날짜 필드"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__day
#, python-format
msgid "Days"
msgstr "일"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Delay"
msgstr "지연"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_range
msgid ""
"Delay after the trigger date. You can put a negative number if you need a "
"delay before the trigger date, like sending a reminder 15 minutes before a "
"meeting."
msgstr ""
"동작되는 날짜 이후로 연기합니다. 동작일 이전으로 날짜를 연기해야 하는 경우에는 음수를 입력하면 되며, 예를 들어 회의 15분 전에 알림"
" 을 전송할 때 사용할 수 있습니다."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range
msgid "Delay after trigger date"
msgstr "트리거 날짜 이후 지연"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_range_type
msgid "Delay type"
msgstr "지연 유형"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Delete Action"
msgstr "작업 삭제"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Deprecated (do not use)"
msgstr "사용 중단됨 (사용하지 않음)"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__description
msgid "Description"
msgstr "설명"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid "Disable Automation Rule"
msgstr "자동화 규칙 비활성화"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"Disabling this automation rule will enable you to continue your workflow\n"
"                but any data created after this could potentially be corrupted,\n"
"                as you are effectively disabling a customization that may set\n"
"                important and/or required fields."
msgstr ""
"자동화된 규칙을 비활성화할 경우 작업 절차는 계속 진행할 수 있으나\n"
"                중요한 또는 필수 필드를 설정할 수 있는\n"
"                사용자 지정 양식을 비활성화하므로\n"
"                이 작업 이후에 생성된 모든 데이터는 손상될 가능성이 있습니다."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__display_name
msgid "Display Name"
msgstr "표시명"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid "Edit Automation Rule"
msgstr "자동화 규칙 편집"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Email"
msgstr "이메일"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Email Events"
msgstr "행사 이메일"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Email, follower or activity action types cannot be used when deleting "
"records, as there are no more records to apply these changes to!"
msgstr ""
"이메일, 팔로워 또는 활동 작업 유형의 변경 사항을 적용할 레코드가 없으므로 더 이상 레코드를 삭제할 때 사용할 수 없습니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Execute Python Code"
msgstr "파이썬 코드 실행"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Execute several actions"
msgstr "몇 가지 작업 실행"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "External"
msgstr "외부"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Extra Conditions"
msgstr "추가 컨디션"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__on_change_field_ids
msgid "Fields that trigger the onchange."
msgstr "onchange를 트리거하는 필드."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Generic User"
msgstr "일반 사용자"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_is_mail_thread
msgid "Has Mail Thread"
msgstr "메일 스레드 있음"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__hour
msgid "Hours"
msgstr "시간"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__id
msgid "ID"
msgstr "ID"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_domain
msgid ""
"If present, this condition must be satisfied before executing the automation"
" rule."
msgstr "조건이 있는 경우, 자동 규칙을 실행하기 전에 해당 조건을 충족해야 합니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__filter_pre_domain
msgid ""
"If present, this condition must be satisfied before the update of the "
"record. Not checked on record creation."
msgstr "이 조건이 있는 경우 레코드를 업데이트하기 전에 조건이 충족되어야 합니다. 레코드 생성 시 확인되지 않습니다."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Keep track of what this automation does and why it exists..."
msgstr "자동화의 기능과 존재 이유를 추적하세요..."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__last_run
msgid "Last Run"
msgstr "최근 실행"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__least_delay_msg
msgid "Least Delay Msg"
msgstr "최소 지연 메시지"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__log_webhook_calls
msgid "Log Calls"
msgstr "통화 기록"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Logs"
msgstr "로그"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Mail event can not be configured on model %s. Only models with discussion "
"feature can be used."
msgstr "모델 %s에 메일 이벤트를 구성할 수 없습니다. 토론 기능이 있는 모델만 사용할 수 있습니다."

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__minutes
msgid "Minutes"
msgstr "분"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_id
msgid "Model"
msgstr "모델"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__model_name
msgid "Model Name"
msgstr "모델명"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid ""
"Model of action %(action_name)s should match the one from automated rule "
"%(rule_name)s."
msgstr "작업 %(action_name)s의 모델은 자동화된 규칙 %(rule_name)s의 모델과 일치해야 합니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__model_id
msgid "Model on which the automation rule runs."
msgstr "자동 규칙이 실행되는 모델입니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trg_date_range_type__month
#, python-format
msgid "Months"
msgstr "월"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "No record to run the automation on was found."
msgstr "자동화를 실행할 레코드를 찾을 수 없습니다."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Note that this automation rule can be triggered up to %d minutes after its "
"schedule."
msgstr "이 자동화 규칙은 예약 후 최대 %d분 후에 트리거할 수 있습니다."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Notes"
msgstr "메모"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__on_change_field_ids
msgid "On Change Fields Trigger"
msgstr "필드 변경을 위한 트리거"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_change
msgid "On UI change"
msgstr "UI 변경 시"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_archive
msgid "On archived"
msgstr "보관됨"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create
msgid "On creation"
msgstr "생성할 때"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unlink
msgid "On deletion"
msgstr "삭제할 때"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_message_received
msgid "On incoming message"
msgstr "수신 메시지"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_message_sent
msgid "On outgoing message"
msgstr "발신 메시지"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_create_or_write
msgid "On save"
msgstr "저장 시"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_unarchive
msgid "On unarchived"
msgstr "보관되지 않음"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_write
msgid "On update"
msgstr "업데이트 시"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_webhook
msgid "On webhook"
msgstr "웹훅에서"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Post as Message"
msgstr "메시지로 게시"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Post as Note"
msgstr "메모로 게시"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_priority_set
msgid "Priority is set to"
msgstr "우선 순위는 다음과 같이 설정됩니다."

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__record_getter
msgid "Record Getter"
msgstr "레코드 게터"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Remove followers"
msgstr "팔로워 삭제"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Remove followers: %(partner_names)s"
msgstr "팔로워 삭제: %(partner_names)s"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Rotate Secret"
msgstr "비밀번호 정기 업데이트"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select a date field..."
msgstr "날짜 필드를 선택하십시오."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select a value..."
msgstr "값 선택..."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Select fields..."
msgstr "필드 선택..."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Send SMS Text Message"
msgstr "SMS 문자메시지 전송"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Send SMS: %(template_name)s"
msgstr "SMS 전송: %(template_name)s"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Send Webhook Notification"
msgstr "웹훅 알림 보내기"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"Send an email when an object changes state, archive records\n"
"                after a month of inactivity or remind yourself to follow-up on\n"
"                tasks when a specific tag is added."
msgstr ""
"상태가 변경되면 이메일을 전송하고, 한 달 이상 활동이 없는 레코드는\n"
"                보관 처리하거나, 특정 태그가 추가되면 후속 작업에 대한\n"
"                알림을 보냅니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Send email"
msgstr "이메일 전송"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Send email: %(template_name)s"
msgstr "이메일 전송: %(template_name)s"

#. module: base_automation
#: model:ir.model,name:base_automation.model_ir_actions_server
msgid "Server Action"
msgstr "서버작업"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_selection_field_id
msgid ""
"Some triggers need a reference to a selection field. This field is used to "
"store it."
msgstr "일부 연결 항목에는 필드를 선택할 수 있도록 참조 항목이 있어야 합니다. 이를 저장하기 위해 이 필드를 사용합니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_field_ref
msgid ""
"Some triggers need a reference to another field. This field is used to store"
" it."
msgstr "일부 연결 항목에는 다른 필드에 대한 참조 항목이 있어야 합니다. 이를 저장하기 위해 이 필드를 사용합니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Specific User"
msgstr "특정 사용자"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_stage_set
msgid "Stage is set to"
msgstr "단계는 다음과 같이 설정됩니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/kanban_header_patch.js:0
#, python-format
msgid "Stage is set to \"%s\""
msgstr "단계는 \"%s\"으로 설정됩니다."

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_state_set
msgid "State is set to"
msgstr "단계는 다음과 같이 설정됩니다."

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_tag_set
msgid "Tag is added"
msgstr "태그가 추가되었습니다."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "Target Record"
msgstr "레코드 대상"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"Target model of actions %(action_names)s are different from rule model."
msgstr "작업 %(action_names)s의 대상 모델이 규칙 모델과 다릅니다."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"The \"%(trigger_value)s\" %(trigger_label)s can only be used with the "
"\"%(state_value)s\" action type"
msgstr ""
"\"%(trigger_value)s\" %(trigger_label)s는 \"%(state_value)s\" 작업 유형에만 사용할 수 "
"있습니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trigger_field_ids
msgid ""
"The automation rule will be triggered if and only if one of these fields is "
"updated.If empty, all fields are watched."
msgstr "해당 필드 중에서 하나를 업데이트한 경우에만 자동화 규칙을 동작시킵니다. 비워둘 경우, 전체 필드를 확인합니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"The error occurred during the execution of the automation rule\n"
"                \""
msgstr ""
"자동화 규칙을 실행하는 동안 오류가 발생했습니다.\n"
"                \""

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__record_getter
msgid ""
"This code will be run to find on which record the automation rule should be "
"run."
msgstr "자동화 규칙을 실행해야 하는 레코드를 검색하기 위해 해당 코드를 실행합니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Timing Conditions"
msgstr "시간 조건"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger
msgid "Trigger"
msgstr "작동 요건"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_id
msgid "Trigger Date"
msgstr "트리거된 날짜"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_selection_field_id
msgid "Trigger Field"
msgstr "트리거 필드"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref_model_name
msgid "Trigger Field Model"
msgstr "트리거 필드 모델"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trigger_field_ids
msgid "Trigger Fields"
msgstr "트리거 필드"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref
msgid "Trigger Reference"
msgstr "트리거 참조"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_field_ref_display_name
msgid "Trigger Reference Display Name"
msgstr "트리거 참조 표시명ㅇ"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.ir_actions_server_view_form_automation
msgid "Type"
msgstr "유형"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "URL"
msgstr "URL"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "URL will be created once the rule is saved."
msgstr "규칙이 저장되면 URL이 생성됩니다."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/ir_actions_server.py:0
#, python-format
msgid "Update"
msgstr "업데이트"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Update the Record"
msgstr "레코드 업데이트"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__url
msgid "Url"
msgstr "Url"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_ir_actions_server__usage
#: model:ir.model.fields,field_description:base_automation.field_ir_cron__usage
msgid "Usage"
msgstr "사용량"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__trg_date_calendar_id
msgid "Use Calendar"
msgstr "일정표 사용"

#. module: base_automation
#: model:ir.model.fields.selection,name:base_automation.selection__base_automation__trigger__on_user_set
msgid "User is set"
msgstr "사용자가 설정되었습니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_trigger_selection_field.js:0
#, python-format
msgid "Values Updated"
msgstr "값 업데이트"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Warning"
msgstr "경고"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Webhook Log"
msgstr "웹훅 로그"

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid "Webhook Logs"
msgstr "웹훅 로그"

#. module: base_automation
#: model:ir.model.fields,field_description:base_automation.field_base_automation__webhook_uuid
msgid "Webhook UUID"
msgstr "웹훅 UUID"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.js:0
#, python-format
msgid "Weeks"
msgstr "주"

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_calendar_id
msgid ""
"When calculating a day-based timed condition, it is possibleto use a "
"calendar to compute the date based on working days."
msgstr "일 기준으로 시간을 지정한 조건을 계산할 때는 근무일을 기준으로 달력에서 날짜를 계산할 수 있습니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__trg_date_id
msgid ""
"When should the condition be triggered.\n"
"                If present, will be checked by the scheduler. If empty, will be checked at creation and update."
msgstr ""
"조건이 작동되어야 하는 경우.\n"
"                해당하는 경우, 스케줄러에서 확인합니다. 공란인 경우에는, 생성하거나 업데이트할 경우 확인합니다."

#. module: base_automation
#: model:ir.model.fields,help:base_automation.field_base_automation__active
msgid "When unchecked, the rule is hidden and will not be executed."
msgstr "선택을 취소하면 규칙은 숨겨지고 실행되지 않습니다."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "When updating"
msgstr "업데이트 시"

#. module: base_automation
#: model_terms:ir.actions.act_window,help:base_automation.base_automation_act
msgid ""
"With Automation Rules, you can automate\n"
"                <em>any</em> workflow."
msgstr ""
"자동화 규칙을 사용하면\n"
"                <em>어떤</em> 워크플로우든 자동화할 수 있습니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid ""
"You can ask an administrator to disable or correct this automation rule."
msgstr "관리자에게 이 자동화 규칙을 비활성화하거나 수정하도록 요청할 수 있습니다."

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_error_dialog.xml:0
#, python-format
msgid "You can disable this automation rule or edit it to solve the issue."
msgstr "이 자동화 규칙을 비활성화하거나 편집하여 문제를 해결할 수 있습니다."

#. module: base_automation
#. odoo-python
#: code:addons/base_automation/models/base_automation.py:0
#, python-format
msgid ""
"You cannot send an email, add followers or create an activity for a deleted "
"record.  It simply does not work."
msgstr ""
"삭제된 레코드에 대해서는 이메일 전송이나 팔로워 추가, 활동 생성 등의 작업을 수행할 수 없습니다. 정상적으로 동작하지 않습니다."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid ""
"Your webhook URL contains a secret. Don't share it online or carelessly."
msgstr "웹훅 URL에는 보안 정보가 포함되어 있습니다. 온라인이나 부주의하게 공유하지 마세요."

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_kanban
msgid "based on"
msgstr "기준"

#. module: base_automation
#: model_terms:ir.ui.view,arch_db:base_automation.view_base_automation_form
msgid "e.g. Support flow"
msgstr "예: 지원 플로우"

#. module: base_automation
#. odoo-javascript
#: code:addons/base_automation/static/src/base_automation_actions_one2many_field.xml:0
#, python-format
msgid "no action defined..."
msgstr "정의된 작업 없음"
