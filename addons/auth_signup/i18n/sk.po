# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_signup
# 
# Translators:
# <PERSON><PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "******-123-4567"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid ""
",<br/><br/>\n"
"                                A password reset was requested for the Odoo account linked to this email.\n"
"                                You may change your password by following this link which will remain valid during 24 hours:<br/>"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
",<br/><br/>\n"
"                        A new device was used to sign in to your account. <br/><br/>\n"
"                        Here are some details about the connection:<br/>"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "--<br/>Mitchell Admin"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "111.222.333.444"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "<span style=\"font-size: 10px;\">Your Account</span><br/>"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                    Browser:</span>"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                    Location:</span>"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                    Platform:</span>"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                Date:</span>"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"<span style=\"font-weight: bold;\">\n"
"                                IP Address:</span>"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid ""
"<strong>A password reset has been requested for this user. An email "
"containing the following link has been sent:</strong>"
msgstr ""
"<strong>Bolo požiadané o resetovanie hesla pre tohto používateľa. Email "
"obsahujúci nasledujúci odkaz bol odoslaný:</strong>"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid ""
"<strong>An invitation email containing the following subscription link has "
"been sent:</strong>"
msgstr ""
"<strong>Emailová pozvánka, obsahujúca nasledujúci odkaz odberu bola "
"odoslaná:</strong>"

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.mail_template_data_unregistered_users
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <t t-set=\"invited_users\" t-value=\"ctx.get('invited_users', [])\"></t>\n"
"                <td style=\"text-align : left\">\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        Pending Invitations\n"
"                    </span><br><br>\n"
"                </td>\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Mitchell Admin</t>,<br> <br>\n"
"                        You added the following user(s) to your database but they haven't registered yet:\n"
"                        <ul>\n"
"                            <t t-foreach=\"invited_users\" t-as=\"invited_user\">\n"
"                                <li t-out=\"invited_user or ''\"><EMAIL></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                        Follow up with them so they can access your database and start working with you.\n"
"                        <br><br>\n"
"                        Have a nice day!<br>\n"
"                        --<br>The <t t-out=\"object.company_id.name or ''\">YourCompany</t> Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.set_password_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Welcome to Odoo</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Marc Demo</t>,<br><br>\n"
"                        You have been invited by <t t-out=\"object.create_uid.name or ''\">OdooBot</t> of <t t-out=\"object.company_id.name or ''\">YourCompany</t> to connect on Odoo.\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"object.signup_url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Accept invitation\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-set=\"website_url\" t-value=\"object.get_base_url()\"></t>\n"
"                        Your Odoo domain is: <b><a t-att-href=\"website_url\" t-out=\"website_url or ''\">http://yourcompany.odoo.com</a></b><br>\n"
"                        Your sign in email is: <b><a t-attf-href=\"/web/login?login={{ object.email }}\" target=\"_blank\" t-out=\"object.email or ''\"><EMAIL></a></b><br><br>\n"
"                        Never heard of Odoo? It’s an all-in-one business software loved by 7+ million users. It will considerably improve your experience at work and increase your productivity.\n"
"                        <br><br>\n"
"                        Have a look at the <a href=\"https://www.odoo.com/page/tour?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo Tour</a> to discover the tool.\n"
"                        <br><br>\n"
"                        Enjoy Odoo!<br>\n"
"                        --<br>The <t t-out=\"object.company_id.name or ''\">YourCompany</t> Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-att-href=\"'mailto:%s' % object.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-att-href=\"'%s' % object.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.mail_template_user_signup_account_created
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Marc Demo</t>,<br><br>\n"
"                        Your account has been successfully created!<br>\n"
"                        Your login is <strong><t t-out=\"object.email or ''\"><EMAIL></t></strong><br>\n"
"                        To gain access to your account, you can use the following link:\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-attf-href=\"/web/login?auth_login={{object.email}}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Go to My Account\n"
"                            </a>\n"
"                        </div>\n"
"                        Thanks,<br>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br>\n"
"                            <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\"><t t-out=\"object.company_id.email or ''\"><EMAIL></t></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\">\n"
"                            <t t-out=\"object.company_id.website or ''\">http://www.example.com</t>\n"
"                        </a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Already have an account?"
msgstr "Máte už účet?"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Another user is already registered using this email address."
msgstr "Iný používateľ je už registrovaný pomocou tejto emailovej adresy."

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Authentication Failed."
msgstr "Autentifikácia zlyhala."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Back to Login"
msgstr "Späť na prihlásenie"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "Browser"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Cannot send email: user %s has no email address."
msgstr "Nemožno odoslať email: používateľ %s nemá žiadnu emailovú adresu."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Change password"
msgstr "Zmeňte heslo"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "City, Region, Country"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Close"
msgstr "Zatvoriť"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Confirm Password"
msgstr "Potvrdiť heslo"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_users__state__active
msgid "Confirmed"
msgstr "Potvrdené"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid ""
"Could not contact the mail server, please check your outgoing email server "
"configuration"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Could not create a new account."
msgstr "Nebolo možné vytvoriť nový účet."

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Could not reset your password"
msgstr "Nebolo možné resetovať vaše heslo"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr "Účet zákazníka"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Dear"
msgstr "Drahý"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "Predvolené prístupové práva"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login
msgid "Don't have an account?"
msgstr "Ešte nemáte účet?"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_reset_password
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Enable password reset from Login page"
msgstr "Povoliť obnovenie hesla z prihlasovacej stránky"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_config_settings__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "Voľná registrácia"

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP smerovanie"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid ""
"If you do not expect this, you can safely ignore this email.<br/><br/>\n"
"                                Thanks,"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid ""
"If you don't recognize it, you should change your password immediately via "
"this link:<br/>"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "In %(country)s"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Invalid signup token"
msgstr "Neplatný prihlasovací token"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Umožnite vašim zákazníkom vstúpiť, aby videli svoje dokumenty"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Multiple accounts found for this login"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Near %(city)s, %(region)s, %(country)s"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Near %(region)s, %(country)s"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_users__state__new
msgid "Never Connected"
msgstr "Nepripojený"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "New Connection to your Account"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "No account found for this login"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "No login provided."
msgstr "Nebol pridelený login"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "OS"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Odoo"
msgstr "Odoo"

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_config_settings__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "Na pozvanie"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "Otherwise, you can safely ignore this email."
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Password"
msgstr "Heslo"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Password Reset"
msgstr "Reset hesla"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Password reset"
msgstr "Reset hesla"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Password reset instructions sent to your email"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Passwords do not match; please retype them."
msgstr "Heslá nie sú totožne; prosím zadajte znova."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "Powered by"
msgstr "Prinášané"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login_successful
msgid "Registration successful."
msgstr ""

#. module: auth_signup
#: model:mail.template,subject:auth_signup.mail_template_data_unregistered_users
msgid "Reminder for unregistered users"
msgstr "Pripomenutie pre neregistrovaných používateľov"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
#: model_terms:ir.ui.view,arch_db:auth_signup.login
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Reset Password"
msgstr "Resetovať heslo"

#. module: auth_signup
#: model:ir.actions.server,name:auth_signup.action_send_password_reset_instructions
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Send Password Reset Instructions"
msgstr "Pošlite pokyny na obnovenie hesla"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Send an Invitation Email"
msgstr "Poslať emailovú pozvánku"

#. module: auth_signup
#: model:mail.template,description:auth_signup.mail_template_data_unregistered_users
msgid ""
"Sent automatically to admin if new user haven't responded to the invitation"
msgstr ""

#. module: auth_signup
#: model:mail.template,description:auth_signup.set_password_email
msgid "Sent to new user after you invited them"
msgstr ""

#. module: auth_signup
#: model:mail.template,description:auth_signup.mail_template_user_signup_account_created
msgid "Sent to portal user who registered themselves"
msgstr ""

#. module: auth_signup
#: model:mail.template,name:auth_signup.set_password_email
msgid "Settings: New Portal Signup"
msgstr ""

#. module: auth_signup
#: model:mail.template,name:auth_signup.mail_template_user_signup_account_created
msgid "Settings: New User Invite"
msgstr ""

#. module: auth_signup
#: model:mail.template,name:auth_signup.mail_template_data_unregistered_users
msgid "Settings: Unregistered User Reminder"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Sign up"
msgstr "Registrácia"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_expiration
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_expiration
msgid "Signup Expiration"
msgstr "Expirácia registrácie"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_token
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_token
msgid "Signup Token"
msgstr "Registračný token"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_type
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_type
msgid "Signup Token Type"
msgstr "Typ registračného tokenu"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_valid
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_valid
msgid "Signup Token is Valid"
msgstr "Registračný token je platný"

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_url
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_url
msgid "Signup URL"
msgstr "URL registrácie"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Signup is not allowed for uninvited users"
msgstr "Registrácia je povolená len pozvanému užívateľovi"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_partner.py:0
#, python-format
msgid "Signup token '%s' is no longer valid"
msgstr "Registračný token '%s' je už neplatný"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_partner.py:0
#, python-format
msgid "Signup token '%s' is not valid"
msgstr "Registračný token '%s' neplatný"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Signup: invalid template user"
msgstr "Registrácia: neplatná šablóna užívateľa"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Signup: no login given for new user"
msgstr "Registrácia: novému užívateľovi nebol pridelený login "

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "Signup: no name or partner given for new user"
msgstr "Registrácia: pre nového užívateľa nebol zadaný názov alebo partner "

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users__state
msgid "Status"
msgstr "Stav"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Google reCaptcha zistil podozrivú aktivitu."

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_template_user_id
msgid "Template user for new users created through signup"
msgstr ""
"Šablóna užívateľa pre nových používateľov vytvorených prostredníctvom "
"registrácie"

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
#, python-format
msgid "The form was not properly filled in."
msgstr "Formulár nebol správne vyplnený."

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid ""
"There was an error when trying to deliver your Email, please check your "
"configuration"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Pre zaslanie pozvania v móde B2B, vyber jeden alebo viacero kontaktov zo "
"zoznamu a klikni na 'Správa portálového prístupu' v rozbaľovacom zozname "
"*Akcie*."

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_users
msgid "User"
msgstr "Užívateľ"

#. module: auth_signup
#: model:ir.actions.server,name:auth_signup.ir_cron_auth_signup_send_pending_user_reminder_ir_actions_server
msgid "Users: Notify About Unregistered Users"
msgstr "Užívatelia: Upozorniť na neregistrovaných používateľov"

#. module: auth_signup
#: model:mail.template,subject:auth_signup.mail_template_user_signup_account_created
msgid "Welcome to {{ object.company_id.name }}!"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
#, python-format
msgid "You cannot perform this action on an archived user."
msgstr "Túto akciu nemôžete vykonať s archivovaným používateľom."

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Your Email"
msgstr "Váš email"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Your Name"
msgstr "Vaše meno"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "YourCompany"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.alert_login_new_device
msgid "day, month dd, yyyy - hh:mm:ss (GMT)"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "e.g. John Doe"
msgstr "napr. Jožko Mrkvička"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "http://www.example.com"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password_email
msgid "<EMAIL>"
msgstr ""

#. module: auth_signup
#: model:mail.template,subject:auth_signup.set_password_email
msgid ""
"{{ object.create_uid.name }} from {{ object.company_id.name }} invites you "
"to connect to Odoo"
msgstr ""
