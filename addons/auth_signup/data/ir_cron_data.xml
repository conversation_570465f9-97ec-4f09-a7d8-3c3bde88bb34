<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_cron_auth_signup_send_pending_user_reminder" model="ir.cron">
        <field name="name">Users: Notify About Unregistered Users</field>
        <field name="model_id" ref="model_res_users"/>
        <field name="state">code</field>
        <field name="code">model.send_unregistered_user_reminder()</field>
        <field name="user_id" ref="base.user_root" />
        <field name="interval_number">1</field>
        <field name="interval_type">days</field>
        <field name="numbercall">-1</field>
    </record>
</odoo>
