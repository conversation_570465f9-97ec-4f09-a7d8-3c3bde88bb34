# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* barcodes
# 
# Translators:
# <PERSON>il <PERSON>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Day<PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid " '*' is not a valid Regex Barcode Pattern. Did you mean '.*'?"
msgstr "'*' 항목은 유효한 정규식 바코드 패턴이 아닙니다.  '.*' 항목을 찾으시나요?"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"<i>Barcodes Nomenclatures</i> define how barcodes are recognized and categorized.\n"
"                                When a barcode is scanned it is associated to the <i>first</i> rule with a matching\n"
"                                pattern. The pattern syntax is that of regular expression, and a barcode is matched\n"
"                                if the regular expression matches a prefix of the barcode."
msgstr ""
"<i>바코드 명칭</i>은 바코드가 인식되고 분류되는 방법을 정의합니다.\n"
"                                바코드를 스캔할 경우 패턴과 일치하는 <i>첫 번째</i> 규칙으로 연결됩니다.\n"
"                               패턴 구문은 정규 표현식의 구문이며\n"
"                               정규 표현식이 바코드의 접두사와 일치하면 바코드가 일치합니다."

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid ""
"A barcode nomenclature defines how the point of sale identify and interprets"
" barcodes"
msgstr "바코드 명칭을 통해 POS에서 바코드를 식별하고 해석하는 방법을 정의합니다."

#. module: barcodes
#: model_terms:ir.actions.act_window,help:barcodes.action_barcode_nomenclature_form
msgid "Add a new barcode nomenclature"
msgstr "새로운 바코드 명칭 추가"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__alias
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__type__alias
msgid "Alias"
msgstr "별칭"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__always
msgid "Always"
msgstr "항상"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__name
msgid "An internal identification for this barcode nomenclature rule"
msgstr "바코드 명명 규칙에 대한 내부 ID"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__name
msgid "An internal identification of the barcode nomenclature"
msgstr "바코드 명명법에 대한 내부 ID"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__any
msgid "Any"
msgstr "또는"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/components/barcode_scanner.xml:0
#, python-format
msgid "Barcode"
msgstr "바코드"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcodes_barcode_events_mixin
msgid "Barcode Event Mixin"
msgstr "바코드 이벤트 혼합"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_nomenclature
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__barcode_nomenclature_id
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Barcode Nomenclature"
msgstr "바코드 명칭"

#. module: barcodes
#: model:ir.actions.act_window,name:barcodes.action_barcode_nomenclature_form
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_tree
msgid "Barcode Nomenclatures"
msgstr "바코드 명명법"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__pattern
msgid "Barcode Pattern"
msgstr "바코드 패턴"

#. module: barcodes
#: model:ir.model,name:barcodes.model_barcode_rule
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_rule_form
msgid "Barcode Rule"
msgstr "바코드 규칙"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcodes_barcode_events_mixin___barcode_scanned
msgid "Barcode Scanned"
msgstr "바코드 스캔됨"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/barcode_handlers.js:0
#, python-format
msgid "Barcode: "
msgstr "바코드:"

#. module: barcodes
#: model:ir.model,name:barcodes.model_res_company
msgid "Companies"
msgstr "회사"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__create_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__create_uid
msgid "Created by"
msgstr "작성자"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__create_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__create_date
msgid "Created on"
msgstr "작성일자"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__display_name
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__display_name
msgid "Display Name"
msgstr "표시명"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__ean13
msgid "EAN-13"
msgstr "EAN-13"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__ean2upc
msgid "EAN-13 to UPC-A"
msgstr "EAN-13에서 UPC-A로"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__ean8
msgid "EAN-8"
msgstr "EAN-8"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__encoding
msgid "Encoding"
msgstr "인코딩"

#. module: barcodes
#: model:ir.model,name:barcodes.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 라우팅"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__id
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__id
msgid "ID"
msgstr "ID"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__write_uid
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__write_date
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__none
msgid "Never"
msgstr "하지 않음"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_res_company__nomenclature_id
msgid "Nomenclature"
msgstr "명명법"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid ""
"Patterns can also define how numerical values, such as weight or price, can be\n"
"                                encoded into the barcode. They are indicated by <code>{NNN}</code> where the N's\n"
"                                define where the number's digits are encoded. Floats are also supported with the\n"
"                                decimals indicated with D's, such as <code>{NNNDD}</code>. In these cases,\n"
"                                the barcode field on the associated records <i>must</i> show these digits as\n"
"                                zeroes."
msgstr ""
"패턴을 이용하여 또한 중량이나 가격과 같은 숫자 값을 바코드에 인코딩하는 방법도\n"
"                                지정할 수 있습니다. 표시 방식은 <code>{NNN}</code>와 같으며 여기에서 N은\n"
"                                숫자의 자릿수가 인코딩되는 위치를 나타냅니다. 변동값도 마찬가지로 지원되며\n"
"                                D로 나타내는 십진수에서도, <code>{NNNDD}</code>와 같이 표시됩니다. 이러한 경우,\n"
"                                연결된 레코드의 바코드 필드에 <i>반드시</i> 해당 숫자가 0으로 표시되어야\n"
"                                합니다."

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/components/barcode_scanner.js:0
#, python-format
msgid "Please, Scan again!"
msgstr "다시 스캔하세요!"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__name
msgid "Rule Name"
msgstr "규칙 이름"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__rule_ids
msgid "Rules"
msgstr "규칙"

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__sequence
msgid "Sequence"
msgstr "순서"

#. module: barcodes
#: model_terms:ir.ui.view,arch_db:barcodes.view_barcode_nomenclature_form
msgid "Tables"
msgstr "표"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/components/barcode_scanner.xml:0
#, python-format
msgid "Tap to scan"
msgstr "탭하여 스캔"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__pattern
msgid "The barcode matching pattern"
msgstr "바코드 일치 패턴"

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"The barcode pattern %(pattern)s does not lead to a valid regular expression."
msgstr "바코드 패턴 %(pattern)s이 유효한 정규식으로 연결되지 않았습니다."

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__rule_ids
msgid "The list of barcode rules"
msgstr "바코드 규칙 목록"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__alias
msgid "The matched pattern will alias to this barcode"
msgstr "일치하는 패턴이 이 바코드의 별칭이 됩니다."

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: a rule can only "
"contain one pair of braces."
msgstr "바코드 규칙 %(pattern)s에 구문 오류가 있습니다: 괄호는 한 쌍만 포함할 수 있습니다. "

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: braces can only "
"contain N's followed by D's."
msgstr "바코드 규칙 %(pattern)s에 구문 오류가 있습니다: 괄호 내 N 다음에는 D 가 반드시 포함되어야 합니다."

#. module: barcodes
#. odoo-python
#: code:addons/barcodes/models/barcode_rule.py:0
#, python-format
msgid ""
"There is a syntax error in the barcode pattern %(pattern)s: empty braces."
msgstr "바코드 규칙 %(pattern)s에 구문 오류가 있습니다: 괄호 내용이 없습니다."

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__encoding
msgid ""
"This rule will apply only if the barcode is encoded with the specified "
"encoding"
msgstr "이 규칙은 바코드가 지정된 인코딩으로 인코딩된 경우에만 적용됩니다."

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_rule__type
msgid "Type"
msgstr "유형"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_nomenclature__upc_ean_conv
msgid ""
"UPC Codes can be converted to EAN by prefixing them with a zero. This "
"setting determines if a UPC/EAN barcode should be automatically converted in"
" one way or another when trying to match a rule with the other encoding."
msgstr ""
"UPC 코드 앞에 0을 붙여 EAN로 변환 할 수 있습니다. 이 설정은 규칙을 다른 인코딩과 일치 시키려고 할 때 UPC/EAN 바코드가"
" 한 방향으로 또는 다른 방식으로 자동 변환되어야 하는지 여부를 결정합니다."

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__encoding__upca
msgid "UPC-A"
msgstr "UPC-A"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_nomenclature__upc_ean_conv__upc2ean
msgid "UPC-A to EAN-13"
msgstr "UPC-A에서 EAN-13 "

#. module: barcodes
#: model:ir.model.fields,field_description:barcodes.field_barcode_nomenclature__upc_ean_conv
msgid "UPC/EAN Conversion"
msgstr "UPC/EAN 변환"

#. module: barcodes
#: model:ir.model.fields.selection,name:barcodes.selection__barcode_rule__type__product
msgid "Unit Product"
msgstr "품목 단위"

#. module: barcodes
#. odoo-javascript
#: code:addons/barcodes/static/src/barcode_handlers.js:0
#, python-format
msgid "Unknown barcode command"
msgstr "알 수 없는 바코드 명령"

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcode_rule__sequence
msgid ""
"Used to order rules such that rules with a smaller sequence match first"
msgstr "작은 순서로 일치하는 것이 우선하도록 규칙을 정렬하는 데 사용됩니다."

#. module: barcodes
#: model:ir.model.fields,help:barcodes.field_barcodes_barcode_events_mixin___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "가장 최근에 스캔한 바코드 값입니다."
