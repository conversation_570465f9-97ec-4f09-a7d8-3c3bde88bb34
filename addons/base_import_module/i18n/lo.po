# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * base_import_module
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:16+0000\n"
"PO-Revision-Date: 2017-10-24 09:00+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Lao (https://www.transifex.com/odoo/teams/41243/lo/)\n"
"Language: lo\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Cancel"
msgstr "ຍົກເລີອກ"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Close"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/controllers/main.py:0
#, python-format
msgid "Could not select database '%s'"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_uid
msgid "Created by"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_date
msgid "Created on"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__display_name
msgid "Display Name"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "File '%s' exceed maximum allowed file size"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__force
msgid "Force init"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,help:base_import_module.field_base_import_module__force
msgid "Force init mode even if installed. (will update `noupdate='1'` records)"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__id
msgid "ID"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import App"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__import_message
msgid "Import Message"
msgstr ""

#. module: base_import_module
#: model:ir.actions.act_window,name:base_import_module.action_view_base_module_import
#: model:ir.model,name:base_import_module.model_base_import_module
#: model:ir.ui.menu,name:base_import_module.menu_view_base_module_import
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import Module"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Import module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__imported
msgid "Imported Module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module____last_update
msgid "Last Modified on"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_uid
msgid "Last Updated by"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_date
msgid "Last Updated on"
msgstr ""

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_module_module
msgid "Module"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__module_file
msgid "Module .ZIP file"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "No file sent."
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Note: you can only import data modules (.xml files and static assets)"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/controllers/main.py:0
#, python-format
msgid "Only administrators can upload a module"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Only zip files are supported."
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Open Modules"
msgstr ""

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Select module package to import (.zip file):"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__state
msgid "Status"
msgstr "ສະພາບ"

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Studio customizations require Studio"
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Studio customizations require the Odoo Studio app."
msgstr ""

#. module: base_import_module
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"Unmet module dependencies: \n"
"\n"
" - %s"
msgstr ""

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_ui_view
msgid "View"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__done
msgid "done"
msgstr ""

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__init
msgid "init"
msgstr ""
