# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import_module
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-18 09:37+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>iam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"\n"
"You may need the Enterprise version to install the data module. Please visit https://www.odoo.com/pricing-plan for more information.\n"
"If you need Website themes, it can be downloaded from https://github.com/odoo/design-themes.\n"
msgstr ""
"\n"
"คุณอาจต้องใช้เวอร์ชัน Enterprise เพื่อติดตั้งโมดูลข้อมูล กรุณาไปที่ https://www.odoo.com/pricing-plan สำหรับข้อมูลเพิ่มเติม\n"
"หากคุณต้องการธีมของเว็บไซต์ สามารถดาวน์โหลดได้จาก https://github.com/odoo/design-themes.\n"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.module_form_apps_inherit
#: model_terms:ir.ui.view,arch_db:base_import_module.module_view_kanban_apps_inherit
msgid "Activate"
msgstr "เปิดใช้งาน"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#: model_terms:ir.ui.view,arch_db:base_import_module.view_module_filter_apps_inherit
#, python-format
msgid "Apps"
msgstr "แอป"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Cancel"
msgstr "ยกเลิก"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Close"
msgstr "ปิด"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Connection to %s failed The list of industry modules cannot be fetched"
msgstr "การเชื่อมต่อกับ %s ล้มเหลว ไม่สามารถดึงรายการโมดูลอุตสาหกรรมได้"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Connection to %s failed, the module %s cannot be downloaded."
msgstr "การเชื่อมต่อกับ %s ล้มเหลว ไม่สามารถดาวน์โหลดโมดูล %s ได้"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/controllers/main.py:0
#, python-format
msgid "Could not select database '%s'"
msgstr "ไม่สามารถเลือกฐานข้อมูล '%s'"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"Error while importing module '%(module)s'.\n"
"\n"
" %(error_message)s \n"
"\n"
msgstr ""
"เกิดข้อผิดพลาดขณะนำเข้าโมดูล '%(module)s'\n"
"\n"
" %(error_message)s \n"
"\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "File '%s' exceed maximum allowed file size"
msgstr "ไฟล์ '%s' มีขนาดใหญ่เกินกว่าที่อนุญาต"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__force
msgid "Force init"
msgstr "บังคับเริ่มต้น"

#. module: base_import_module
#: model:ir.model.fields,help:base_import_module.field_base_import_module__force
msgid ""
"Force init mode even if installed. (will update `noupdate='1'` records)"
msgstr ""
"บังคับโหมดเริ่มต้นแม้ว่าจะติดตั้งแล้วก็ตาม (จะอัปเดตบันทึก `noupdate='1'')"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__id
msgid "ID"
msgstr "ไอดี"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__import_message
msgid "Import Message"
msgstr "นำเข้าข้อความ"

#. module: base_import_module
#: model:ir.actions.act_window,name:base_import_module.action_view_base_module_import
#: model:ir.model,name:base_import_module.model_base_import_module
#: model:ir.ui.menu,name:base_import_module.menu_view_base_module_import
msgid "Import Module"
msgstr "นำเข้าโมดูล"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__with_demo
msgid "Import demo data of module"
msgstr "นำเข้าข้อมูลสาธิตของโมดูล"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__imported
msgid "Imported Module"
msgstr "โมดูลที่นำเข้า"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__ir_module_module__module_type__industries
msgid "Industries"
msgstr "อุตสาหกรรม"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Install"
msgstr "ติดตั้ง"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Install an Industry"
msgstr "ติดตั้งอุตสาหกรรม"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Install the application"
msgstr "ติดตั้งแอปพลิเคชัน"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Load demo data"
msgstr "ตัวอย่าง ข้อมูล"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"Load demo data to test the industry's features with sample records. Do not "
"load them if this is your production database."
msgstr ""
"โหลดข้อมูลสาธิตเพื่อทดสอบฟีเจอร์ของอุตสาหกรรมด้วยบันทึกตัวอย่าง "
"ไม่ต้องโหลดหากนี่คือฐานข้อมูลที่ใช้งานจริงของคุณ"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_module_module
msgid "Module"
msgstr "โมดูล"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__module_file
msgid "Module .ZIP file"
msgstr "ไฟล์ .ZIP ของโมดูล"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__module_type
msgid "Module Type"
msgstr "ประเภทโมดูล"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "ถอนการติดตั้งโมดูล"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Module file (.zip)"
msgstr "ไฟล์โมดูล (.zip)"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__modules_dependencies
msgid "Modules Dependencies"
msgstr "การพึ่งพาโมดูล"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "No file sent."
msgstr "ไม่มีไฟล์ที่ส่ง"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Note: you can only import data modules (.xml files and static assets)"
msgstr ""
"หมายเหตุ: คุณสามารถนำเข้าได้เฉพาะโมดูลข้อมูล (ไฟล์ .xml และสินทรัพย์คงที่)"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__ir_module_module__module_type__official
msgid "Official Apps"
msgstr "แอปอย่างเป็นทางการ"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Only administrators can install data modules."
msgstr "เฉพาะผู้ดูแลระบบเท่านั้นที่สามารถติดตั้งโมดูลข้อมูลได้"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/controllers/main.py:0
#, python-format
msgid "Only administrators can upload a module"
msgstr "มีเพียงผู้ดูแลระบบเท่านั้นที่สามารถอัปโหลดโมดูลได้"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Only zip files are supported."
msgstr "รองรับเฉพาะไฟล์ zip เท่านั้น"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__state
msgid "Status"
msgstr "สถานะ"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Studio customizations require the Odoo Studio app."
msgstr "การปรับแต่งสตูดิโอต้องใช้แอปสตูดิโอของ Odoo"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"The installation of the data module would fail as the following dependencies"
" can't be found in the addons-path:\n"
msgstr ""
"การติดตั้งโมดูลข้อมูลจะล้มเหลว เนื่องจากไม่พบการขึ้นต่อกันต่อไปนี้ในเส้นทาง "
"addons:\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"The list of industry applications cannot be fetched. Please try again later"
msgstr ""
"ไม่สามารถดึงรายการแอปพลิเคชันอุตสาหกรรมได้ กรุณาลองใหม่อีกครั้งในภายหลัง"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "The module %s cannot be downloaded"
msgstr "ไม่สามารถดาวน์โหลดโมดูล %s ได้"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Unknown module dependencies:"
msgstr "การอ้างอิงโมดูลที่ไม่รู้จัก:"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.module_form_apps_inherit
#: model_terms:ir.ui.view,arch_db:base_import_module.module_view_kanban_apps_inherit
msgid "Upgrade"
msgstr "อัพเกรด"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_ui_view
msgid "View"
msgstr "ดู"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__done
msgid "done"
msgstr "เสร็จสิ้น"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__init
msgid "init"
msgstr "init"
