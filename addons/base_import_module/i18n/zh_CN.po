# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import_module
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> CHEN <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Wil <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-18 09:37+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"\n"
"You may need the Enterprise version to install the data module. Please visit https://www.odoo.com/pricing-plan for more information.\n"
"If you need Website themes, it can be downloaded from https://github.com/odoo/design-themes.\n"
msgstr ""
"\n"
"您可能需要企业版才能安装数据模块。请访问 https://www.odoo.com/pricing-plan 获取更多信息。\n"
"如果需要网站主题，可从 https://github.com/odoo/design-themes 下载。\n"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.module_form_apps_inherit
#: model_terms:ir.ui.view,arch_db:base_import_module.module_view_kanban_apps_inherit
msgid "Activate"
msgstr "启用"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#: model_terms:ir.ui.view,arch_db:base_import_module.view_module_filter_apps_inherit
#, python-format
msgid "Apps"
msgstr "应用"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Cancel"
msgstr "取消"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Close"
msgstr "关闭"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Connection to %s failed The list of industry modules cannot be fetched"
msgstr "与 %s 的连接失败 无法获取行业模块列表"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Connection to %s failed, the module %s cannot be downloaded."
msgstr "与 %s 的连接失败，无法下载模块 %s。"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/controllers/main.py:0
#, python-format
msgid "Could not select database '%s'"
msgstr "无法选择数据库 '%s'"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_uid
msgid "Created by"
msgstr "创建人"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_date
msgid "Created on"
msgstr "创建日期"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"Error while importing module '%(module)s'.\n"
"\n"
" %(error_message)s \n"
"\n"
msgstr ""
"导入模块时出错 '%(module)s'.\n"
"\n"
" %(error_message)s \n"
"\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "File '%s' exceed maximum allowed file size"
msgstr "文件 '%s' 超过了允许的大小"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__force
msgid "Force init"
msgstr "强制初始化"

#. module: base_import_module
#: model:ir.model.fields,help:base_import_module.field_base_import_module__force
msgid ""
"Force init mode even if installed. (will update `noupdate='1'` records)"
msgstr "即使已经安装也强制初始化（将更新 `noupdate='1'` 记录）"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__id
msgid "ID"
msgstr "ID"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__import_message
msgid "Import Message"
msgstr "导入消息"

#. module: base_import_module
#: model:ir.actions.act_window,name:base_import_module.action_view_base_module_import
#: model:ir.model,name:base_import_module.model_base_import_module
#: model:ir.ui.menu,name:base_import_module.menu_view_base_module_import
msgid "Import Module"
msgstr "导入模块"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__with_demo
msgid "Import demo data of module"
msgstr "导入模块的演示数据"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__imported
msgid "Imported Module"
msgstr "导入的模块"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__ir_module_module__module_type__industries
msgid "Industries"
msgstr "工业"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Install"
msgstr "安装"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Install an Industry"
msgstr "安装行业方案"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Install the application"
msgstr "安装应用程序"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Load demo data"
msgstr "装入样例数据"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"Load demo data to test the industry's features with sample records. Do not "
"load them if this is your production database."
msgstr "加载演示数据来测试行业功能。如果这是您的生产数据库，则不要加载它们。"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_module_module
msgid "Module"
msgstr "模块"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__module_file
msgid "Module .ZIP file"
msgstr "模块 .ZIP 文件"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__module_type
msgid "Module Type"
msgstr "模块类型"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "模块卸载"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Module file (.zip)"
msgstr "模块文件(.zip)"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__modules_dependencies
msgid "Modules Dependencies"
msgstr "模块依赖关系"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "No file sent."
msgstr "没有文件发送."

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Note: you can only import data modules (.xml files and static assets)"
msgstr "注意：只能导入数据模块（后缀名是.xml文件和静态资源）"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__ir_module_module__module_type__official
msgid "Official Apps"
msgstr "官方应用程序"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Only administrators can install data modules."
msgstr "只有管理员才能安装数据模块。"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/controllers/main.py:0
#, python-format
msgid "Only administrators can upload a module"
msgstr "只有管理员能够上传模块"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Only zip files are supported."
msgstr "仅支持zip格式的文件。"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__state
msgid "Status"
msgstr "状态"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Studio customizations require the Odoo Studio app."
msgstr "Studio 定制需要安装 Odoo Studio应用。"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"The installation of the data module would fail as the following dependencies"
" can't be found in the addons-path:\n"
msgstr ""
"数据模块的安装会失败，因为在 addons-path 中找不到以下依赖项：\n"
"\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"The list of industry applications cannot be fetched. Please try again later"
msgstr "无法获取行业申请列表。请稍后再试"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "The module %s cannot be downloaded"
msgstr "模块 %s 无法下载"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Unknown module dependencies:"
msgstr "未知模块依赖关系："

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.module_form_apps_inherit
#: model_terms:ir.ui.view,arch_db:base_import_module.module_view_kanban_apps_inherit
msgid "Upgrade"
msgstr "升级"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_ui_view
msgid "View"
msgstr "视图"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__done
msgid "done"
msgstr "完成"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__init
msgid "init"
msgstr "init"
