# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import_module
# 
# Translators:
# <PERSON>, 2023
# <PERSON> Villalo<PERSON> López, 2023
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>il <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-18 09:37+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"\n"
"You may need the Enterprise version to install the data module. Please visit https://www.odoo.com/pricing-plan for more information.\n"
"If you need Website themes, it can be downloaded from https://github.com/odoo/design-themes.\n"
msgstr ""
"\n"
"Es posible que necesite la versión Enterprise para instalar el módulo de datos. Visite https://www.odoo.com/pricing-plan para obtener más información.\n"
"Si necesita temas para el sitio web puede descargarlos en https://github.com/odoo/design-themes.\n"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.module_form_apps_inherit
#: model_terms:ir.ui.view,arch_db:base_import_module.module_view_kanban_apps_inherit
msgid "Activate"
msgstr "Activar"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#: model_terms:ir.ui.view,arch_db:base_import_module.view_module_filter_apps_inherit
#, python-format
msgid "Apps"
msgstr "Aplicaciones"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Cancel"
msgstr "Cancelar"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Close"
msgstr "Cerrar"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Connection to %s failed The list of industry modules cannot be fetched"
msgstr ""
"Falló la conexión con %s . No se puede recuperar la lista de módulos del "
"sector. "

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Connection to %s failed, the module %s cannot be downloaded."
msgstr "Falló la conexión con %s , no se puede descargar el módulo %s"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/controllers/main.py:0
#, python-format
msgid "Could not select database '%s'"
msgstr "No se pudo seleccionar la base de datos '%s'"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__create_date
msgid "Created on"
msgstr "Creado el"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"Error while importing module '%(module)s'.\n"
"\n"
" %(error_message)s \n"
"\n"
msgstr ""
"Error al importar el módulo '%(module)s'.\n"
"\n"
" %(error_message)s \n"
"\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "File '%s' exceed maximum allowed file size"
msgstr "El archivo '%s' excedió el máximo tamaño de archivo permitido"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__force
msgid "Force init"
msgstr "Forzar inicio"

#. module: base_import_module
#: model:ir.model.fields,help:base_import_module.field_base_import_module__force
msgid ""
"Force init mode even if installed. (will update `noupdate='1'` records)"
msgstr ""
"Forzar el modo de inicio incluso si está instalado (actualizará los "
"registros de `noupdate='1'`)"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__id
msgid "ID"
msgstr "ID"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__import_message
msgid "Import Message"
msgstr "Importar mensaje"

#. module: base_import_module
#: model:ir.actions.act_window,name:base_import_module.action_view_base_module_import
#: model:ir.model,name:base_import_module.model_base_import_module
#: model:ir.ui.menu,name:base_import_module.menu_view_base_module_import
msgid "Import Module"
msgstr "Importar módulo"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__with_demo
msgid "Import demo data of module"
msgstr "Importar datos de demostración del módulo"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__imported
msgid "Imported Module"
msgstr "Módulo importado"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__ir_module_module__module_type__industries
msgid "Industries"
msgstr "Sectores"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Install"
msgstr "Instalar"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Install an Industry"
msgstr "Instalar un sector"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Install the application"
msgstr "Instalar la aplicación"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Load demo data"
msgstr "Cargar datos de prueba"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"Load demo data to test the industry's features with sample records. Do not "
"load them if this is your production database."
msgstr ""
"Cargar datos de demostración para probar las funciones del sector con "
"registros de ejemplo. No cargue estos datos si esta es su base de datos de "
"producción."

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_module_module
msgid "Module"
msgstr "Módulo"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__module_file
msgid "Module .ZIP file"
msgstr "Archivo .ZIP del módulo"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_ir_module_module__module_type
msgid "Module Type"
msgstr "Tipo de módulo"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Desinstalar módulo"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Module file (.zip)"
msgstr "Archivo del módulo (.zip)"

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__modules_dependencies
msgid "Modules Dependencies"
msgstr "Dependencias del módulo"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "No file sent."
msgstr "Ningún archivo enviado."

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.view_base_module_import
msgid "Note: you can only import data modules (.xml files and static assets)"
msgstr ""
"Nota: solo puede importar módulos de datos (archivos .xml y activos "
"estáticos)"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__ir_module_module__module_type__official
msgid "Official Apps"
msgstr "Aplicaciones oficiales"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Only administrators can install data modules."
msgstr "Solo los administradores pueden instalar módulos de datos."

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/controllers/main.py:0
#, python-format
msgid "Only administrators can upload a module"
msgstr "Sólo los administradores pueden actualizar un módulo"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Only zip files are supported."
msgstr "Solo se admiten archivos zip."

#. module: base_import_module
#: model:ir.model.fields,field_description:base_import_module.field_base_import_module__state
msgid "Status"
msgstr "Estado"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Studio customizations require the Odoo Studio app."
msgstr "Las personalizaciones de Studio requieren la aplicación Odoo Studio."

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"The installation of the data module would fail as the following dependencies"
" can't be found in the addons-path:\n"
msgstr ""
"La instalación del módulo de datos fallaría, ya que las siguientes "
"dependencias no se pueden encontrar en la ruta de complementos (addons-"
"path):\n"

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid ""
"The list of industry applications cannot be fetched. Please try again later"
msgstr ""
"No se puede recuperar la lista de aplicaciones del sector. Inténtelo de "
"nuevo más tarde."

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "The module %s cannot be downloaded"
msgstr "No se puede descargar el módulo %s "

#. module: base_import_module
#. odoo-python
#: code:addons/base_import_module/models/ir_module.py:0
#, python-format
msgid "Unknown module dependencies:"
msgstr "Dependencias de módulos desconocidas:"

#. module: base_import_module
#: model_terms:ir.ui.view,arch_db:base_import_module.module_form_apps_inherit
#: model_terms:ir.ui.view,arch_db:base_import_module.module_view_kanban_apps_inherit
msgid "Upgrade"
msgstr "Actualizar"

#. module: base_import_module
#: model:ir.model,name:base_import_module.model_ir_ui_view
msgid "View"
msgstr "Vista"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__done
msgid "done"
msgstr "hecho"

#. module: base_import_module
#: model:ir.model.fields.selection,name:base_import_module.selection__base_import_module__state__init
msgid "init"
msgstr "init"
