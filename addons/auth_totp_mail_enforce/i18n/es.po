# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_mail_enforce
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid ""
".\n"
"                <br/>"
msgstr ""
".\n"
"                <br/>"

#. module: auth_totp_mail_enforce
#: model:mail.template,body_html:auth_totp_mail_enforce.mail_template_totp_mail_code
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    Dear <t t-out=\"object.partner_id.name or ''\"></t><br><br>\n"
"    <p>Someone is trying to log in into your account with a new device.</p>\n"
"    <ul>\n"
"        <t t-set=\"not_available\">N/A</t>\n"
"        <li>Location: <t t-out=\"ctx.get('location') or not_available\"></t></li>\n"
"        <li>Device: <t t-out=\"ctx.get('device') or not_available\"></t></li>\n"
"        <li>Browser: <t t-out=\"ctx.get('browser') or not_available\"></t></li>\n"
"        <li>IP address: <t t-out=\"ctx.get('ip') or not_available\"></t></li>\n"
"    </ul>\n"
"    <p>If this is you, please enter the following code to complete the login:</p>\n"
"    <t t-set=\"code_expiration\" t-value=\"object._get_totp_mail_code()\"></t>\n"
"    <t t-set=\"code\" t-value=\"code_expiration[0]\"></t>\n"
"    <t t-set=\"expiration\" t-value=\"code_expiration[1]\"></t>\n"
"    <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <span t-out=\"code\" style=\"background-color:#faf9fa; border: 1px solid #dad8de; padding: 8px 16px 8px 16px; font-size: 24px; color: #875A7B; border-radius: 5px;\"></span>\n"
"    </div>\n"
"    <small>Please note that this code expires in <t t-out=\"expiration\"></t>.</small>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        If you did NOT initiate this log-in,\n"
"        you should immediately change your password to ensure account security.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        We also strongly recommend enabling the two-factor authentication using an authenticator app to help secure your account.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"            Activate my two-factor authentication\n"
"        </a>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    Estimado/a <t t-out=\"object.partner_id.name or ''\"></t><br><br>\n"
"    <p>Alguien está intentando iniciar sesión con su cuenta desde un dispositivo nuevo.</p>\n"
"    <ul>\n"
"        <t t-set=\"not_available\">N/A</t>\n"
"        <li>Ubicación: <t t-out=\"ctx.get('location') or not_available\"></t></li>\n"
"        <li>Dispositivo: <t t-out=\"ctx.get('device') or not_available\"></t></li>\n"
"        <li>Navegador: <t t-out=\"ctx.get('browser') or not_available\"></t></li>\n"
"        <li>Dirección IP: <t t-out=\"ctx.get('ip') or not_available\"></t></li>\n"
"    </ul>\n"
"    <p>Si es usted, ingrese el siguiente código para poder iniciar sesión:</p>\n"
"    <t t-set=\"code_expiration\" t-value=\"object._get_totp_mail_code()\"></t>\n"
"    <t t-set=\"code\" t-value=\"code_expiration[0]\"></t>\n"
"    <t t-set=\"expiration\" t-value=\"code_expiration[1]\"></t>\n"
"    <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <span t-out=\"code\" style=\"background-color:#faf9fa; border: 1px solid #dad8de; padding: 8px 16px 8px 16px; font-size: 24px; color: #875A7B; border-radius: 5px;\"></span>\n"
"    </div>\n"
"    <small>Tenga en cuenta que este código vence en <t t-out=\"expiration\"></t>.</small>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        Si no es usted quien está iniciando sesión,\n"
"        cambie su contraseña inmediatamente para mejorar la seguridad.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        También le recomendamos activar la autenticación de dos factores con una aplicación de autenticación para ayudarle a asegurar su cuenta.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"            Activar la autenticación de dos factores\n"
"        </a>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid ""
"<i class=\"fa fa-envelope-o\"/>\n"
"                To login, enter below the six-digit authentication code just sent via email to"
msgstr ""
"<i class=\"fa fa-envelope-o\"/>\n"
"                Para iniciar sesión ingrese el código de verificación de seis dígitos que se le envió al correo"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__res_config_settings__auth_totp_policy__all_required
msgid "All users"
msgstr "Todos los usuarios"

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
#, python-format
msgid "Cannot send email: user %s has no email address."
msgstr ""
"No se puede enviar el correo electrónico: el usuario %s no tiene dirección "
"de correo electrónico."

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__auth_totp_rate_limit_log__limit_type__code_check
msgid "Code Checking"
msgstr "Comprobación del código"

#. module: auth_totp_mail_enforce
#: model:ir.model,name:auth_totp_mail_enforce.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__create_date
msgid "Created on"
msgstr "Creado el"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__res_config_settings__auth_totp_policy__employee_required
msgid "Employees only"
msgstr "Solo empleados"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.res_config_settings_view_form
msgid ""
"Enforce the two-factor authentication by email for employees or for all "
"users (including portal users) if they didn't enable any other two-factor "
"authentication method."
msgstr ""
"Establezca la autenticación de dos factores por correo electrónico para los "
"empleados o todos los usuarios (incluyendo los usuarios del portal) si no "
"activaron este método."

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_res_config_settings__auth_totp_enforce
msgid "Enforce two-factor authentication"
msgstr "Forzar la autenticación de dos factores"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__id
msgid "ID"
msgstr "ID"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__ip
msgid "Ip"
msgstr "IP"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid "Learn More"
msgstr "Aprenda más"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__limit_type
msgid "Limit Type"
msgstr "Tipo de límite"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid "Re-send email"
msgstr "Reenviar correo electrónico"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__scope
msgid "Scope"
msgstr "Alcance"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__auth_totp_rate_limit_log__limit_type__send_email
msgid "Send Email"
msgstr "Enviar correo electrónico"

#. module: auth_totp_mail_enforce
#: model:mail.template,name:auth_totp_mail_enforce.mail_template_totp_mail_code
msgid "Settings: 2Fa New Login"
msgstr "Configuración: nuevo inicio de sesión A2F"

#. module: auth_totp_mail_enforce
#: model:ir.model,name:auth_totp_mail_enforce.model_auth_totp_rate_limit_log
msgid "TOTP rate limit logs"
msgstr "Cantidad límite de registros de TOTP"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_res_config_settings__auth_totp_policy
msgid "Two-factor authentication enforcing policy"
msgstr "Política de cumplimiento de autenticación de dos factores"

#. module: auth_totp_mail_enforce
#: model:ir.model,name:auth_totp_mail_enforce.model_res_users
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__user_id
msgid "User"
msgstr "Usuario"

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
#, python-format
msgid "Verification failed, please double-check the 6-digit code"
msgstr "La verificación falló, revise el código de 6 dígitos"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid ""
"We strongly recommend enabling the two-factor authentication using an authenticator app to help secure your account.\n"
"                <br/>"
msgstr ""
"Le recomendamos activar la autenticación de dos factores con la aplicación de verificación para mantener su cuenta segura.\n"
"                <br/>"

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
#, python-format
msgid "You reached the limit of authentication mails sent for your account"
msgstr ""
"Se alcanzó el límite de correos de autenticación enviados a su cuenta."

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
#, python-format
msgid "You reached the limit of code verifications for your account"
msgstr "Se alcanzó el límite de códigos de autenticación para su cuenta."

#. module: auth_totp_mail_enforce
#: model:mail.template,subject:auth_totp_mail_enforce.mail_template_totp_mail_code
msgid "Your two-factor authentication code"
msgstr "Su código de autenticación de dos factores"
