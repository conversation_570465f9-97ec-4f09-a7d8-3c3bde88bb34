# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp_mail_enforce
# 
# Translators:
# <PERSON> <m<PERSON><PERSON><PERSON>@outlook.com>, 2023
# <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# コフスタジオ, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: コフスタジオ, 2024\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid ""
".\n"
"                <br/>"
msgstr ""
".\n"
"                <br/>"

#. module: auth_totp_mail_enforce
#: model:mail.template,body_html:auth_totp_mail_enforce.mail_template_totp_mail_code
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    Dear <t t-out=\"object.partner_id.name or ''\"></t><br><br>\n"
"    <p>Someone is trying to log in into your account with a new device.</p>\n"
"    <ul>\n"
"        <t t-set=\"not_available\">N/A</t>\n"
"        <li>Location: <t t-out=\"ctx.get('location') or not_available\"></t></li>\n"
"        <li>Device: <t t-out=\"ctx.get('device') or not_available\"></t></li>\n"
"        <li>Browser: <t t-out=\"ctx.get('browser') or not_available\"></t></li>\n"
"        <li>IP address: <t t-out=\"ctx.get('ip') or not_available\"></t></li>\n"
"    </ul>\n"
"    <p>If this is you, please enter the following code to complete the login:</p>\n"
"    <t t-set=\"code_expiration\" t-value=\"object._get_totp_mail_code()\"></t>\n"
"    <t t-set=\"code\" t-value=\"code_expiration[0]\"></t>\n"
"    <t t-set=\"expiration\" t-value=\"code_expiration[1]\"></t>\n"
"    <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <span t-out=\"code\" style=\"background-color:#faf9fa; border: 1px solid #dad8de; padding: 8px 16px 8px 16px; font-size: 24px; color: #875A7B; border-radius: 5px;\"></span>\n"
"    </div>\n"
"    <small>Please note that this code expires in <t t-out=\"expiration\"></t>.</small>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        If you did NOT initiate this log-in,\n"
"        you should immediately change your password to ensure account security.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        We also strongly recommend enabling the two-factor authentication using an authenticator app to help secure your account.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"            Activate my two-factor authentication\n"
"        </a>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    Dragi <t t-out=\"object.partner_id.name or ''\"></t><br><br>\n"
"    <p>Neko pokušava da se prijavi na vaš nalog pomoću novog uređaja.</p>\n"
"    <ul>\n"
"        <t t-set=\"not_available\">N/A</t>\n"
"        <li>Lokacija: <t t-out=\"ctx.get('location') or not_available\"></t></li>\n"
"        <li>Device: <t t-out=\"ctx.get('device') or not_available\"></t></li>\n"
"        <li>Browser: <t t-out=\"ctx.get('browser') or not_available\"></t></li>\n"
"        <li>IP adressa: <t t-out=\"ctx.get('ip') or not_available\"></t></li>\n"
"    </ul>\n"
"    <p>Ako ste to vi, unesite sledeći kôd da biste dovršili prijavljivanje:</p>\n"
"    <t t-set=\"code_expiration\" t-value=\"object._get_totp_mail_code()\"></t>\n"
"    <t t-set=\"code\" t-value=\"code_expiration[0]\"></t>\n"
"    <t t-set=\"expiration\" t-value=\"code_expiration[1]\"></t>\n"
"    <div style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <span t-out=\"code\" style=\"background-color:#faf9fa; border: 1px solid #dad8de; padding: 8px 16px 8px 16px; font-size: 24px; color: #875A7B; border-radius: 5px;\"></span>\n"
"    </div>\n"
"    <small>Upamtite da kod ističe <t t-out=\"expiration\"></t>.</small>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        Ako niste pokrenuli ovo prijavljivanje,\n"
"        trebalo bi odmah da promenite lozinku da biste osigurali bezbednost naloga.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px;\">\n"
"        Takođe preporučujemo omogućavanje potvrde identiteta sa dva faktora pomoću aplikacije za potvrdu identiteta radi obezbeđivanja naloga.\n"
"    </p>\n"
"\n"
"    <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"        <a t-att-href=\"object.get_totp_invite_url()\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"            Activirajte 2-faktorsku autentifikaciju\n"
"        </a>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid ""
"<i class=\"fa fa-envelope-o\"/>\n"
"                To login, enter below the six-digit authentication code just sent via email to"
msgstr ""
"<i class=\"fa fa-envelope-o\"/>\n"
"            Da se prijavite, unesite ispod šestocifreni kod za autentikaciju koji je poslat emailom"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__res_config_settings__auth_totp_policy__all_required
msgid "All users"
msgstr "Svi korisnici"

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
#, python-format
msgid "Cannot send email: user %s has no email address."
msgstr "Nije moguće poslati email: korisnik %s nema email adresu."

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__auth_totp_rate_limit_log__limit_type__code_check
msgid "Code Checking"
msgstr "Kod provere"

#. module: auth_totp_mail_enforce
#: model:ir.model,name:auth_totp_mail_enforce.model_res_config_settings
msgid "Config Settings"
msgstr "Podešavanje konfiguracije"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__res_config_settings__auth_totp_policy__employee_required
msgid "Employees only"
msgstr "Samo zaposleni"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.res_config_settings_view_form
msgid ""
"Enforce the two-factor authentication by email for employees or for all "
"users (including portal users) if they didn't enable any other two-factor "
"authentication method."
msgstr ""
"Sprovedite dvofaktornu autentifikaciju putem e-pošte za zaposlene ili za sve"
" korisnike (uključujući korisnike portala) ako nisu omogućili bilo koji "
"drugi metod dvofaktorne autentifikacije."

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_res_config_settings__auth_totp_enforce
msgid "Enforce two-factor authentication"
msgstr "Sprovedite dvofaktornu autentifikaciju"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__id
msgid "ID"
msgstr "ID"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__ip
msgid "Ip"
msgstr "Ip"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__write_uid
msgid "Last Updated by"
msgstr "Poslednji put ažurirao"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__write_date
msgid "Last Updated on"
msgstr "Poslednji put ažurirano"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid "Learn More"
msgstr "Saznaj više"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__limit_type
msgid "Limit Type"
msgstr "Tip ograničenja"

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid "Re-send email"
msgstr "Ponovo pošalji email"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__scope
msgid "Scope"
msgstr "Obim"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields.selection,name:auth_totp_mail_enforce.selection__auth_totp_rate_limit_log__limit_type__send_email
msgid "Send Email"
msgstr "Pošalji Email"

#. module: auth_totp_mail_enforce
#: model:mail.template,name:auth_totp_mail_enforce.mail_template_totp_mail_code
msgid "Settings: 2Fa New Login"
msgstr "Podešavanja: 2Fa Novo logovanje"

#. module: auth_totp_mail_enforce
#: model:ir.model,name:auth_totp_mail_enforce.model_auth_totp_rate_limit_log
msgid "TOTP rate limit logs"
msgstr "TOTP rate limit logs"

#. module: auth_totp_mail_enforce
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_res_config_settings__auth_totp_policy
msgid "Two-factor authentication enforcing policy"
msgstr "Dvostruki faktor autentifikacije sprovodi politiku"

#. module: auth_totp_mail_enforce
#: model:ir.model,name:auth_totp_mail_enforce.model_res_users
#: model:ir.model.fields,field_description:auth_totp_mail_enforce.field_auth_totp_rate_limit_log__user_id
msgid "User"
msgstr "Korisnik"

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
#, python-format
msgid "Verification failed, please double-check the 6-digit code"
msgstr "Verifikacija nije uspela, molimo vas da proverite 6-cifreni kod."

#. module: auth_totp_mail_enforce
#: model_terms:ir.ui.view,arch_db:auth_totp_mail_enforce.auth_totp_mail_form
msgid ""
"We strongly recommend enabling the two-factor authentication using an authenticator app to help secure your account.\n"
"                <br/>"
msgstr ""
"Preporučujemo da omogućite dvofaktornu autentifikaciju koristeći aplikaciju za autentifikaciju kako biste osigurali svoj nalog.\n"
"<br/>"

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
#, python-format
msgid "You reached the limit of authentication mails sent for your account"
msgstr "Dostigli ste limit poslatih autentifikacionih mejlova za vaš nalog."

#. module: auth_totp_mail_enforce
#. odoo-python
#: code:addons/auth_totp_mail_enforce/models/res_users.py:0
#, python-format
msgid "You reached the limit of code verifications for your account"
msgstr "Dostigli ste limit verifikacija koda za vaš nalog."

#. module: auth_totp_mail_enforce
#: model:mail.template,subject:auth_totp_mail_enforce.mail_template_totp_mail_code
msgid "Your two-factor authentication code"
msgstr "Vaš kod dvofaktorske autentikacije"
