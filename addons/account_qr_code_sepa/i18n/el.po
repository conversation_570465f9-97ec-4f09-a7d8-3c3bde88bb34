# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_qr_code_sepa
# 
# Translators:
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_qr_code_sepa
#: model:ir.model,name:account_qr_code_sepa.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Τραπεζικοί Λογαριασμοί"

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
#, python-format
msgid "Can't generate a SEPA QR Code with the %s currency."
msgstr ""

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
#, python-format
msgid "Can't generate a SEPA QR code if the account type isn't IBAN."
msgstr ""

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
#, python-format
msgid "Can't generate a SEPA QR code with a non SEPA iban."
msgstr ""

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
#, python-format
msgid "SEPA Credit Transfer QR"
msgstr ""

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
#, python-format
msgid ""
"The account receiving the payment must have an account holder name or "
"partner name set."
msgstr ""
