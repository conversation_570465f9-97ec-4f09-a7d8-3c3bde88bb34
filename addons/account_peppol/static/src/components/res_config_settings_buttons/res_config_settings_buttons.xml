<?xml version="1.0" encoding="UTF-8"?>

<templates>
    <t t-name="account_peppol.ActionButtons">
        <xpath expr="//div[hasclass('action_buttons')]" position="inside">
            <div class="d-flex" colspan="3">
                <div class="mt-3">
                    <button type="button"
                            class="btn btn-primary"
                            t-on-click="createUser"
                            t-if="proxyState === 'not_registered'">
                            <t t-out="createUserButtonLabel"/>
                    </button>
                </div>
                <div class="mt-3">
                    <button type="button"
                            class="btn btn-secondary me-1"
                            t-on-click="deregister"
                            t-if="proxyState === 'active' and migrationPrepared === false">
                            <t t-out="deregisterUserButtonLabel"/>
                    </button>
                </div>
                <div class="mt-3">
                    <button type="button"
                            class="btn btn-primary"
                            t-on-click="updateDetails"
                            t-if="['pending', 'manually_approved', 'active'].includes(proxyState)">
                            Update contact details
                    </button>
                </div>
                <div class="mt-3">
                    <button type="button"
                            class="btn btn-primary"
                            t-on-click="checkCode"
                            t-if="proxyState === 'sent_verification'">
                            Confirm
                    </button>
                </div>
                <div class="mt-3">
                    <button type="button"
                            class="btn btn-primary"
                            t-on-click="sendCode"
                            t-if="proxyState === 'not_verified'">
                            Verify mobile number
                    </button>
                </div>
                <div class="mt-3">
                    <button type="button"
                            class="btn btn-secondary ms-1"
                            t-on-click="sendCode"
                            t-att-disabled="this.state.isSmsButtonDisabled"
                            t-if="proxyState === 'sent_verification'">
                            Send again
                    </button>
                </div>
            </div>
        </xpath>
    </t>
</templates>
