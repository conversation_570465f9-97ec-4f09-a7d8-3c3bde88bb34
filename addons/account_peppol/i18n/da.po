# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_peppol
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2023
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid ""
"<br/>\n"
"                            If you need a Peppol compliant software, we recommend"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_move_form
msgid ""
"<span class=\"mx-1\" invisible=\"not peppol_is_demo_uuid\"> (Demo)</span>\n"
"                    <span class=\"text-muted mx-3\" invisible=\"peppol_move_state != 'to_send'\">\n"
"                        The invoice will be sent automatically to PEPPOL\n"
"                    </span>"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">\n"
"                                    Peppol Details\n"
"                                </span>\n"
"                                <span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\"/>"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"<span class=\"text-info\" invisible=\"not account_peppol_edi_mode == 'demo'\"> (Demo)</span>\n"
"                                        <span class=\"text-info\" invisible=\"not account_peppol_edi_mode == 'test'\"> (Test)</span>"
msgstr ""
"<span class=\"text-info\" invisible=\"not account_peppol_edi_mode == 'demo'\"> (Demo)</span>\n"
"                                        <span class=\"text-info\" invisible=\"not account_peppol_edi_mode == 'test'\"> (Test)</span>"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"<span>\n"
"                                    I want to migrate my Peppol connection to Odoo (optional):\n"
"                                </span>"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid ""
"A participant with these details has already been registered on the network."
" If you have previously registered to an alternative Peppol service, please "
"deregister from that service, or request a migration key before trying "
"again. "
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
#, python-format
msgid "A purchase journal must be used to receive Peppol documents."
msgstr ""

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr "Konto EDI proxy bruger"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move_send
msgid "Account Move Send"
msgstr "Send kontobevægelse"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_mode
msgid "Account Peppol Edi Mode"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_move_send__account_peppol_edi_mode_info
msgid "Account Peppol Edi Mode Info"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_mode_constraint
msgid "Account Peppol Mode Constraint"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_journal__is_peppol_journal
msgid "Account used for Peppol"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__active
msgid "Active"
msgstr "Aktiv"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Application status:"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"By clicking the button below I accept that Odoo may process my e-invoices."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid ""
"Can't cancel an active registration. Please request a migration or "
"deregister instead."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Can't cancel registration with this status: %s"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Can't deregister with this status: %s"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_move_form
msgid "Cancel PEPPOL"
msgstr "Annullér PEPPOL"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Cancel registration"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__canceled
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__canceled
msgid "Canceled"
msgstr "Annulleret"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_move.py:0
#, python-format
msgid "Cannot cancel an entry that has already been sent to PEPPOL"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__account_peppol_verification_label__not_valid_format
msgid "Cannot receive this format"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Cannot register a user with a %s application"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__account_peppol_validity_last_check
#: model:ir.model.fields,field_description:account_peppol.field_res_users__account_peppol_validity_last_check
msgid "Checked on"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_company__peppol_eas
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_eas
msgid ""
"Code used to identify the Endpoint for BIS Billing 3.0 and its derivatives.\n"
"             List available at https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"
msgstr ""

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_company
msgid "Companies"
msgstr "Virksomheder"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
#, python-format
msgid "Confirm"
msgstr "Bekræft"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Connection error, please try again later."
msgstr ""

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid "Contact details were updated."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Contact email and mobile number are required."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send.py:0
#: model:ir.model.fields.selection,name:account_peppol.selection__res_config_settings__account_peppol_edi_mode__demo
#: model:ir.model.fields.selection,name:account_peppol.selection__res_config_settings__account_peppol_mode_constraint__demo
#, python-format
msgid "Demo"
msgstr "Demo"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid "Deregister from Peppol"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__done
msgid "Done"
msgstr "Udført"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_user
msgid "EDI user"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_edi_identification
msgid "Edi Identification"
msgstr "EDI Identifikation"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_move_send__enable_peppol
msgid "Enable Peppol"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__error
msgid "Error"
msgstr "Fejl"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Fetch Peppol invoice status"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Fetch from Peppol"
msgstr "Hent fra Peppol"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid ""
"In Belgium, electronic invoicing will be <u>mandatory as of January "
"2026</u>."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"In demo mode sending and receiving invoices is simulated. There will be no "
"communication with the Peppol network."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Incoming Invoices Journal"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__is_peppol_edi_format
#: model:ir.model.fields,field_description:account_peppol.field_res_users__is_peppol_edi_format
msgid "Is Peppol Edi Format"
msgstr ""

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: account_peppol
#: model:ir.model,name:account_peppol.model_account_move
msgid "Journal Entry"
msgstr "Journalbilag"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_partner__account_peppol_validity_last_check
#: model:ir.model.fields,help:account_peppol.field_res_users__account_peppol_validity_last_check
msgid "Last Peppol endpoint verification"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_config_settings__account_peppol_edi_mode__prod
#: model:ir.model.fields.selection,name:account_peppol.selection__res_config_settings__account_peppol_mode_constraint__prod
msgid "Live"
msgstr "Live"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_migration_key
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_migration_key
msgid "Migration Key"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Migration key"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Mobile Number"
msgstr "Mobilnummer"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_phone_number
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_phone_number
msgid "Mobile number (for validation)"
msgstr "Mobilnummer (til validering)"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__not_registered
msgid "Not registered"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__account_peppol_verification_label__not_valid
msgid "Not valid"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__not_verified
msgid "Not verified"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__account_peppol_verification_label__not_verified
msgid "Not verified yet"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid "Odoo"
msgstr "Odoo"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_edi_proxy_client_user__proxy_type__peppol
msgid "PEPPOL"
msgstr "PEPPOL"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__is_account_peppol_participant
msgid "PEPPOL Participant"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_purchase_journal_id
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_purchase_journal_id
msgid "PEPPOL Purchase Journal"
msgstr "PEPPOL indkøbsjournal"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__account_peppol_is_endpoint_valid
#: model:ir.model.fields,field_description:account_peppol.field_res_users__account_peppol_is_endpoint_valid
msgid "PEPPOL endpoint validity"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_bank_statement_line__peppol_message_uuid
#: model:ir.model.fields,field_description:account_peppol.field_account_move__peppol_message_uuid
#: model:ir.model.fields,field_description:account_peppol.field_account_payment__peppol_message_uuid
msgid "PEPPOL message ID"
msgstr "PEPPOL besked ID"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_bank_statement_line__peppol_move_state
#: model:ir.model.fields,field_description:account_peppol.field_account_journal__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_account_move__peppol_move_state
#: model:ir.model.fields,field_description:account_peppol.field_account_payment__peppol_move_state
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_proxy_state
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_proxy_state
msgid "PEPPOL status"
msgstr "PEPPOL status"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_new_documents_ir_actions_server
msgid "PEPPOL: retrieve new documents"
msgstr "PEPPOL: hent nye dokumenter"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_message_status_ir_actions_server
msgid "PEPPOL: update message status"
msgstr "PEPPOL: opdatér beskedstatus"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.ir_cron_peppol_get_participant_status_ir_actions_server
msgid "PEPPOL: update participant status"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid ""
"PS: <b style=\"color: $o-enterprise-action-color;\">We did not send your "
"invoice on Peppol.</b>"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.mail_notification_layout_with_responsible_signature_and_peppol
msgid ""
"PS: This invoice has also been <b style=\"color: $o-enterprise-action-"
"color\">sent on Peppol</b>."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__pending
msgid "Pending"
msgstr "Afventer"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__processing
msgid "Pending Reception"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_view_tree
msgid "Peppol EAS"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_endpoint
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_endpoint
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_view_tree
msgid "Peppol Endpoint"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_bank_statement_line__peppol_is_demo_uuid
#: model:ir.model.fields,field_description:account_peppol.field_account_move__peppol_is_demo_uuid
#: model:ir.model.fields,field_description:account_peppol.field_account_payment__peppol_is_demo_uuid
msgid "Peppol Is Demo Uuid"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_account_invoice_filter
msgid "Peppol Ready"
msgstr "Peppol klar"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_journal.py:0
#, python-format
msgid "Peppol Ready invoices"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_view_tree
msgid "Peppol Validity"
msgstr "Peppol gyldighed"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#, python-format
msgid "Peppol document has been received successfully"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__peppol_eas
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_eas
msgid "Peppol e-address (EAS)"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_partner__account_peppol_verification_label
#: model:ir.model.fields,field_description:account_peppol.field_res_users__account_peppol_verification_label
msgid "Peppol endpoint validity"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#, python-format
msgid "Peppol error: %s"
msgstr "Peppol fejl: %s"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_journal_dashboard_kanban_view
msgid "Peppol ready invoices"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.account_peppol_view_account_invoice_filter
msgid "Peppol status"
msgstr "Peppol status"

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#, python-format
msgid "Peppol status update: %s"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"Please do not hesitate to contact our support if you need further "
"assistance."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Please enter a mobile number to verify your application."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Please enter a primary contact email to verify your application."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
#, python-format
msgid ""
"Please enter the mobile number in the correct international format.\n"
"For example: +***********, where +32 is the country code.\n"
"Currently, only European countries are supported."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/account_edi_proxy_user.py:0
#, python-format
msgid "Please fill in the EAS code and the Participant ID code."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
#, python-format
msgid "Please install the phonenumbers library."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send.py:0
#, python-format
msgid "Please verify partner configuration in partner settings."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_company__account_peppol_contact_email
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_contact_email
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Primary contact email"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_company__account_peppol_contact_email
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_contact_email
msgid "Primary contact email for Peppol-related communication"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_edi_proxy_client_user__proxy_type
msgid "Proxy Type"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__to_send
msgid "Queued"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__ready
msgid "Ready to send"
msgstr "Klar til at sende"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__is_account_peppol_participant
msgid "Register as a PEPPOL user"
msgstr "Registrér som en PEPPOL bruger"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__rejected
msgid "Rejected"
msgstr "Afvist"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_edi_proxy_client_user__peppol_verification_code
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_verification_code
msgid "SMS verification code"
msgstr "SMS bekræftelseskode"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
#, python-format
msgid "Send again"
msgstr "Send igen"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_account_move_send__checkbox_send_peppol
msgid "Send the invoice via PEPPOL"
msgstr "Send fakturaen via PEPPOL"

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_move_send__checkbox_send_peppol
msgid "Send via PEPPOL"
msgstr "Send via PEPPOL"

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__account_move__peppol_move_state__skipped
msgid "Skipped"
msgstr "Sprunget over"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"Start sending and receiving documents via Peppol as soon as your "
"registration is complete."
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid "Switch to Live"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send.py:0
#: model:ir.model.fields.selection,name:account_peppol.selection__res_config_settings__account_peppol_edi_mode__test
#: model:ir.model.fields.selection,name:account_peppol.selection__res_config_settings__account_peppol_mode_constraint__test
#, python-format
msgid "Test"
msgstr "Test"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid ""
"Test mode allows registration of the user on the test Peppol network.\n"
"                                        By clicking the button below I accept that Odoo may process my e-invoices."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_company.py:0
#, python-format
msgid "The Peppol endpoint identification number is not correct."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "The Peppol service that is used is likely to be %s."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send.py:0
#, python-format
msgid "The document has been sent to the Peppol Access Point for processing"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid ""
"The endpoint number might not be correct. Please check if you entered the "
"right identification number."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send.py:0
#, python-format
msgid ""
"The following partners are not correctly configured to receive Peppol "
"documents. Please check and verify their Peppol endpoint and the Electronic "
"Invoicing format: %s"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/wizard/account_move_send.py:0
#, python-format
msgid "The partner is missing Peppol EAS and/or Endpoint identifier."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_partner__account_peppol_is_endpoint_valid
#: model:ir.model.fields,help:account_peppol.field_res_users__account_peppol_is_endpoint_valid
msgid "The partner's EAS code and PEPPOL endpoint are valid"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/tools/demo_utils.py:0
#, python-format
msgid ""
"The peppol status of the documents has been reset when switching from Demo "
"to Live."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid ""
"The recommended EAS code for Belgium is 0208. The Endpoint should be the "
"Company Registry number."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "The rejection reason has been sent to you via email."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_edi_identification
msgid "The unique id that identifies this user, typically the vat"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "The verification code is not correct"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "The verification code should contain six digits."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid ""
"This feature is deprecated. Contact odoo support if you need a migration "
"key."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "This verification code has expired. Please request a new one."
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid ""
"To generate complete electronic invoices, also set a country for this "
"partner."
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Too many attempts to request an SMS code. Please try again later."
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_company__peppol_endpoint
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_endpoint
msgid ""
"Unique identifier used by the BIS Billing 3.0 and its derivatives, also "
"known as 'Endpoint ID'."
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
#, python-format
msgid "Update contact details"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__is_account_peppol_participant
msgid "Use PEPPOL"
msgstr "Anvend PEPPOL"

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_mode_constraint
msgid ""
"Using the config params, this field specifies which edi modes may be "
"selected from the UI"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_partner__account_peppol_verification_label__valid
msgid "Valid"
msgstr "Gyldig"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid "Validate registration"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid "Validate registration (Demo)"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid "Validate registration (Test)"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields.selection,name:account_peppol.selection__res_company__account_peppol_proxy_state__sent_verification
msgid "Verification code sent"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid "Verify"
msgstr "Verificer"

#. module: account_peppol
#: model:ir.actions.server,name:account_peppol.partner_action_verify_peppol
msgid "Verify Peppol"
msgstr "Verificér Peppol"

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.xml:0
#, python-format
msgid "Verify mobile number"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_partner_form_account_peppol
msgid "Verify partner's PEPPOL endpoint"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,field_description:account_peppol.field_account_move_send__peppol_warning
#: model:ir.model.fields,field_description:account_peppol.field_res_config_settings__account_peppol_endpoint_warning
msgid "Warning"
msgstr "Advarsel"

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "We sent a verification code to"
msgstr ""

#. module: account_peppol
#. odoo-javascript
#: code:addons/account_peppol/static/src/components/res_config_settings_buttons/res_config_settings_buttons.js:0
#, python-format
msgid ""
"You will not be able to send or receive Peppol documents in Odoo anymore. "
"Are you sure you want to proceed?"
msgstr ""

#. module: account_peppol
#: model:ir.model.fields,help:account_peppol.field_res_company__account_peppol_phone_number
#: model:ir.model.fields,help:account_peppol.field_res_config_settings__account_peppol_phone_number
msgid "You will receive a verification code to this mobile number"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your Peppol identification is:"
msgstr ""

#. module: account_peppol
#. odoo-python
#: code:addons/account_peppol/models/res_config_settings.py:0
#, python-format
msgid "Your confirmation code is"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your migration key is:"
msgstr ""

#. module: account_peppol
#: model_terms:ir.ui.view,arch_db:account_peppol.res_config_settings_view_form
msgid "Your registration should be activated within a day."
msgstr ""
