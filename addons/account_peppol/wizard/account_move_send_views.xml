<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="account_move_send_inherit_account_peppol">
            <field name="name">account.move.send.form.inherit.peppol</field>
            <field name="model">account.move.send</field>
            <field name="inherit_id" ref="account.account_move_send_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[@name='warnings']" position="inside">
                    <field name="enable_peppol" invisible="1"/>
                    <div class="alert alert-warning" role="alert" invisible="not checkbox_send_peppol or not peppol_warning or ubl_partner_warning">
                        <!-- hiding the warning if we're already showing one through the
                        UBL module to avoid showing basically the same errors at the same time -->
                        <field name="peppol_warning"/>
                    </div>
                </xpath>
                <xpath expr="//div[@name='option_send_mail']" position='after'>
                    <div name="option_peppol"
                         invisible="not enable_peppol">
                        <field name="checkbox_send_peppol"/>
                        <b>
                            <label for="checkbox_send_peppol"/>
                        </b>
                        <field name="account_peppol_edi_mode_info" class="oe_inline o_form_label"/>
                    </div>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
