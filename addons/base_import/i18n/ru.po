# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# Сергей <PERSON>е<PERSON>анин <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON>, 2024
# Collex100, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "%s at multiple rows"
msgstr "%s в нескольких рядах"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "%s records successfully imported"
msgstr "%s записей успешно импортировано"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect."
msgstr ""
"В файле обнаружен один столбец, что часто означает неправильный разделитель "
"файлов."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Additional Fields"
msgstr "Дополнительные поля"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Advanced"
msgstr "Дополнительно"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Allow matching with subfields"
msgstr "Разрешить сопоставление с подполями"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid ""
"An unknown issue occurred during import (possibly lost connection, data "
"limit exceeded or memory limits exceeded). Please retry in case the issue is"
" transient. If the issue still occurs, try to split the file rather than "
"import it at once."
msgstr ""
"Во время импорта возникла неизвестная проблема (возможно, потеряно "
"соединение, превышен лимит данных или памяти). Пожалуйста, повторите "
"попытку, если проблема носит временный характер. Если проблема сохраняется, "
"попробуйте разделить файл, а не импортировать его сразу."

#. module: base_import
#: model:ir.model,name:base_import.model_base
msgid "Base"
msgstr "База"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "Base Import"
msgstr "Импорт Базы"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_mapping
msgid "Base Import Mapping"
msgstr "Сопоставление базового импорта"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Batch"
msgstr "Порция"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Batch Import"
msgstr "Пакетный импорт"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Batch limit"
msgstr "Ограничение партии"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Cancel"
msgstr "Отменить"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Click 'Resume' to proceed with the import, resuming at line %s."
msgstr ""
"Нажмите \"Возобновить\", чтобы продолжить импорт, возобновив его со строки "
"%s."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Column %s contains incorrect values (value: %s)"
msgstr "Колонка %s содержит неверные значения (значение: %s)"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Column %s contains incorrect values. Error in line %d: %s"
msgstr "Столбец %s содержит неверные значения. Ошибка в строке %d: %s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__column_name
msgid "Column Name"
msgstr "Название столбца"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#: code:addons/base_import/static/src/import_model.js:0
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Comma"
msgstr "Запятая"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Comments"
msgstr "Комментарии"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Could not retrieve URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"
msgstr ""
"Не удалось получить URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Create new values"
msgstr "Создание новых значений"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_uid
msgid "Created by"
msgstr "Создано"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_date
msgid "Created on"
msgstr "Создано"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Database ID"
msgstr "ID базы данных"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Date Format:"
msgstr "Формат даты:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Datetime Format:"
msgstr "Формат даты и времени:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Decimals Separator:"
msgstr "Разделитель десятичных дробей:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Dot"
msgstr "Точка"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Download"
msgstr "Скачать"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Encoding:"
msgstr "Кодировка:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Error Parsing Date [%s:L%d]: %s"
msgstr "Ошибка парсинга данных [ %s: L %d]: %s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Error at row %s: \"%s\""
msgstr "Ошибка в строке %s: \"%s\""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Error while importing records: Text Delimiter should be a single character."
msgstr ""
"Ошибка при импорте записей: Разделитель текста должен состоять из одного "
"символа."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Error while importing records: all rows should be of the same size, but the "
"title row has %d entries while the first row has %d. You may need to change "
"the separator character."
msgstr ""
"Ошибка при импорте записей: все строки должны быть одинакового размера, но в"
" заглавной строке содержится %d записей, а в первой - %d. Возможно, "
"необходимо изменить символ-разделитель."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Estimated time left:"
msgstr "Предполагаемое оставшееся время:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Everything seems valid."
msgstr "Вроде всё правильно."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Excel files are recommended as formatting is automatic."
msgstr ""
"Рекомендуется использовать файлы Excel, так как форматирование происходит "
"автоматически."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "External ID"
msgstr "Внешний ID"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__field_name
msgid "Field Name"
msgstr "Имя поля"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file
msgid "File"
msgstr "Файл"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "File Column"
msgstr "Колонка файлов"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_name
msgid "File Name"
msgstr "Имя файла"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_type
msgid "File Type"
msgstr "Тип файла"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "File size exceeds configured maximum (%s bytes)"
msgstr "Размер файла превышает заданный максимум (%s байт)"

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import__file
msgid "File to check and/or import, raw binary (not base64)"
msgstr ""
"Файл для проверки и/или импорта, необработанный двоичный файл (не base64)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Finalizing current batch before interrupting..."
msgstr "Завершение работы над текущей партией перед прерыванием..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "For CSV files, you may need to select the correct separator."
msgstr "Для файлов CSV может потребоваться выбрать правильный разделитель."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Formatting"
msgstr "Форматирование"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Found invalid image data, images should be imported as either URLs or "
"base64-encoded data."
msgstr ""
"Найденные недействительные данные изображений, изображения должны быть "
"импортированы либо как URL, либо как данные в базовой 64-кодировке."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Go to Import FAQ"
msgstr "Перейти к FAQ по импорту"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Help"
msgstr "Помощь"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Here is the start of the file we could not import:"
msgstr "Вот начало файла, который мы не смогли импортировать:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__id
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__id
msgid "ID"
msgstr "ID"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid ""
"If the file contains\n"
"                    the column names, Odoo can try auto-detecting the\n"
"                    field corresponding to the column. This makes imports\n"
"                    simpler especially when the file has many columns."
msgstr ""
"Если файл содержит\n"
"                    имена столбцов, Odoo может попробовать автоматически определить\n"
"                    поле, соответствующее столбцу. Это делает импорт\n"
"                    особенно если файл содержит много столбцов."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid ""
"If the model uses openchatter, history tracking will set up subscriptions "
"and send notifications during the import, but lead to a slower import."
msgstr ""
"Если у данной модели есть комментарии, будут настроены подписки и отправлены"
" уведомления, но это может сильно замедлить импорт."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr ""
"Размер изображения слишком велик, импортируемые изображения должны быть "
"меньше 42 миллионов пикселей"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Import"
msgstr "Импорт"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Import FAQ"
msgstr "Импортировать FAQ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "Import a File"
msgstr "Импорт файла"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Import file has no content or is corrupt"
msgstr "Файл импорта не содержит содержимого или поврежден"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Import preview failed due to: \""
msgstr "Предварительный просмотр импорта не удался из-за: \""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_records/import_records.xml:0
#, python-format
msgid "Import records"
msgstr "Импорт записей"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Imported file"
msgstr "Импортированный файл"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "Importing"
msgstr "Импорт"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Invalid cell format at row %(row)s, column %(col)s: %(cell_value)s, with "
"format: %(cell_format)s, as (%(format_type)s) formats are not supported."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Invalid cell value at row %(row)s, column %(col)s: %(cell_value)s"
msgstr ""
"Недействительное значение ячейки в строке %(row)s, столбец %(col)s: "
"%(cell_value)s"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Load File"
msgstr "Загрузка файла"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "Loading file..."
msgstr "Загрузка файла..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_block_ui.xml:0
#, python-format
msgid "Loading..."
msgstr "Загрузка..."

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__res_model
msgid "Model"
msgstr "Модель"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "Multiple errors occurred"
msgstr "Произошло несколько ошибок"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Need Help?"
msgstr "Нужна помощь?"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "No Separator"
msgstr "Без разделителя"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "No matching records found for the following name"
msgstr "Не найдено ни одной подходящей записи для следующего имени"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Odoo Field"
msgstr "Поле Odoo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Prevent import"
msgstr "Предотвращение импорта"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Preview"
msgstr "Предпросмотр"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Progress bar"
msgstr "Прогресс-бар"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Reimport"
msgstr "Реимпорт"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Relation Fields"
msgstr "Поля отношений"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__res_model
msgid "Res Model"
msgstr "Res Model"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Resume"
msgstr "Продолжить"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Search a field..."
msgstr "Поиск по полю..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "See possible values"
msgstr "См. возможные значения"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Selected Sheet:"
msgstr "Избранный лист:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Semicolon"
msgstr "Запятая"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Separator:"
msgstr "Разделитель:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Set to: %s"
msgstr "Установить на: %s"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Set to: False"
msgstr "Установить на: False"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Set to: True"
msgstr "Установить на: True"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Set value as empty"
msgstr "Установите значение как пустое"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Sheet:"
msgstr "Лист:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Skip record"
msgstr "Пропустить запись"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Space"
msgstr "Пробел"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Standard Fields"
msgstr "Стандартные поля"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Start at line"
msgstr "Начните с линии"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Stop Import"
msgstr "Остановить импорт"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Suggested Fields"
msgstr "Предлагаемые поля"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Tab"
msgstr "Вкладка"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Test"
msgstr "Тест"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "Testing"
msgstr "Тестирование"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Text Delimiter:"
msgstr "Ограничитель текста:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "The file contains blocking errors (see below)"
msgstr "Файл содержит ошибки блокировки (см. ниже)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "The file will be imported by batches"
msgstr "Файл будет импортироваться партиями"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "This column will be concatenated in field"
msgstr "Этот столбец будет конкатенирован в поле"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Thousands Separator:"
msgstr "Разделитель тысяч:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "To import multiple values, separate them by a comma."
msgstr "Чтобы импортировать несколько значений, разделите их запятой."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "To import, select a field..."
msgstr "Для импорта выберите поле..."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Track history during import"
msgstr "Отслеживание истории при импорте"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr ""
"Невозможно загрузить файл \"{extension}\": требуется модуль Python "
"\"{modname}\""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr ""
"Неподдерживаемый формат файла \"{}\", импорт поддерживает только CSV, ODS, "
"XLS и XLSX"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Untitled"
msgstr "Без названия"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Upload File"
msgstr "Загрузить файл"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Upload an Excel or CSV file to import"
msgstr "Загрузите файл Excel или CSV для импорта"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid ""
"Use HH for hours in a 24h system, use II in conjonction with 'p' for a 12h "
"system. You can use a custom format in addition to the suggestions provided."
" Leave empty to let Odoo guess the format (recommended)"
msgstr ""
"Используйте HH для обозначения часов в 24-часовой системе, II в сочетании с "
"'p' - в 12-часовой системе. Вы можете использовать собственный формат в "
"дополнение к предложенным. Оставьте пустым, чтобы Odoo сам догадался о "
"формате (рекомендуется)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid ""
"Use YYYY to represent the year, MM for the month and DD for the day. Include"
" separators such as a dot, forward slash or dash. You can use a custom "
"format in addition to the suggestions provided. Leave empty to let Odoo "
"guess the format (recommended)"
msgstr ""
"Используйте YYYY для обозначения года, MM для месяца и DD для дня. Включите "
"разделители, такие как точка, прямая косая черта или тире. Вы можете "
"использовать собственный формат в дополнение к предложенным. Оставить "
"пустым, чтобы Odoo сам догадался о формате (рекомендуется)"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Use first row as header"
msgstr "Используйте первый ряд в качестве заголовка"

#. module: base_import
#: model:ir.model,name:base_import.model_res_users
msgid "User"
msgstr "Пользователь"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid ""
"Warning: ignores the labels line, empty lines and lines composed only of "
"empty cells"
msgstr ""
"Предупреждение: будут проигнорированы строки с метками, пустые строки и "
"строки, содержащие только пустые ячейки"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.xml:0
#, python-format
msgid "When a value cannot be matched:"
msgstr "Когда значение не может быть сопоставлено:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"You can not import images via URL, check with your administrator or support "
"for the reason."
msgstr ""
"Вы не можете импортировать изображения с помощью URL, обратитесь к "
"администратору или в техподдержку."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "You can test or reload your file before resuming the import."
msgstr ""
"Вы можете протестировать или перезагрузить файл перед возобновлением "
"импорта."

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "You must configure at least one field to import"
msgstr "Вы должны настроить хотя бы одно поле для импорта"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "at multiple rows"
msgstr "в несколько рядов"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "at row"
msgstr "в ряд"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "in field"
msgstr "в поле"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "minutes"
msgstr "минут"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "more"
msgstr "больше"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "out of"
msgstr "из"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "seconds"
msgstr "секунд"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "unknown error code %s"
msgstr "неизвестный код ошибки %s"
