# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_import
# 
# Translators:
# Wil <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "%s at multiple rows"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "%s records successfully imported"
msgstr "%s registos importados"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid ""
"A single column was found in the file, this often means the file separator "
"is incorrect."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Additional Fields"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Advanced"
msgstr "Avançado"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Allow matching with subfields"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid ""
"An unknown issue occurred during import (possibly lost connection, data "
"limit exceeded or memory limits exceeded). Please retry in case the issue is"
" transient. If the issue still occurs, try to split the file rather than "
"import it at once."
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_base
msgid "Base"
msgstr "Base"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_import
msgid "Base Import"
msgstr "Importação base"

#. module: base_import
#: model:ir.model,name:base_import.model_base_import_mapping
msgid "Base Import Mapping"
msgstr "Mapeamento da importação base"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Batch"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Batch Import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Batch limit"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Cancel"
msgstr "Cancelar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Click 'Resume' to proceed with the import, resuming at line %s."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Column %s contains incorrect values (value: %s)"
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Column %s contains incorrect values. Error in line %d: %s"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__column_name
msgid "Column Name"
msgstr "Nome da coluna "

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#: code:addons/base_import/static/src/import_model.js:0
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Comma"
msgstr "Vírgula"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Comments"
msgstr "Comentários"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Could not retrieve URL: %(url)s [%(field_name)s: L%(line_number)d]: "
"%(error)s"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Create new values"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__create_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__create_date
msgid "Created on"
msgstr "Criado em"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Database ID"
msgstr "Id. da Base de Dados"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Date Format:"
msgstr "Formato da data"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Datetime Format:"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Decimals Separator:"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__display_name
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__display_name
msgid "Display Name"
msgstr "Nome"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Dot"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Download"
msgstr "Transferir"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Encoding:"
msgstr "Codificação:"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Error Parsing Date [%s:L%d]: %s"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Error at row %s: \"%s\""
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Error while importing records: Text Delimiter should be a single character."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Error while importing records: all rows should be of the same size, but the "
"title row has %d entries while the first row has %d. You may need to change "
"the separator character."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Estimated time left:"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Everything seems valid."
msgstr "Tudo parece válido."

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Excel files are recommended as formatting is automatic."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "External ID"
msgstr "Id. Externa"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__field_name
msgid "Field Name"
msgstr "Nome do Campo"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file
msgid "File"
msgstr "Ficheiro"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "File Column"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_name
msgid "File Name"
msgstr "Nome do Ficheiro"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__file_type
msgid "File Type"
msgstr "Tipo de ficheiro"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "File size exceeds configured maximum (%s bytes)"
msgstr ""

#. module: base_import
#: model:ir.model.fields,help:base_import.field_base_import_import__file
msgid "File to check and/or import, raw binary (not base64)"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Finalizing current batch before interrupting..."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "For CSV files, you may need to select the correct separator."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Formatting"
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Found invalid image data, images should be imported as either URLs or "
"base64-encoded data."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Go to Import FAQ"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Help"
msgstr "Ajuda"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Here is the start of the file we could not import:"
msgstr "Aqui está uma pré-visualização do ficheiro que não foi importado:"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__id
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__id
msgid "ID"
msgstr "ID"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid ""
"If the file contains\n"
"                    the column names, Odoo can try auto-detecting the\n"
"                    field corresponding to the column. This makes imports\n"
"                    simpler especially when the file has many columns."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid ""
"If the model uses openchatter, history tracking will set up subscriptions "
"and send notifications during the import, but lead to a slower import."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Image size excessive, imported images must be smaller than 42 million pixel"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Import"
msgstr "Importar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Import FAQ"
msgstr "Importar FAQ"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "Import a File"
msgstr "Importar um ficheiro"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Import file has no content or is corrupt"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Import preview failed due to: \""
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_records/import_records.xml:0
#, python-format
msgid "Import records"
msgstr "Importar registos"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Imported file"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "Importing"
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Invalid cell format at row %(row)s, column %(col)s: %(cell_value)s, with "
"format: %(cell_format)s, as (%(format_type)s) formats are not supported."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Invalid cell value at row %(row)s, column %(col)s: %(cell_value)s"
msgstr ""

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_uid
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__write_date
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Load File"
msgstr "Carregar Ficheiro"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "Loading file..."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_block_ui.xml:0
#, python-format
msgid "Loading..."
msgstr "A carregar..."

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_import__res_model
msgid "Model"
msgstr "Modelo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "Multiple errors occurred"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Need Help?"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "No Separator"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "No matching records found for the following name"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Odoo Field"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Prevent import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Preview"
msgstr "Pré-visualizar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Progress bar"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Reimport"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Relation Fields"
msgstr "Campos de relação"

#. module: base_import
#: model:ir.model.fields,field_description:base_import.field_base_import_mapping__res_model
msgid "Res Model"
msgstr "Modelo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Resume"
msgstr "Resumo"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Search a field..."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "See possible values"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Selected Sheet:"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Semicolon"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Separator:"
msgstr "Separador:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Set to: %s"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Set to: False"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Set to: True"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Set value as empty"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Sheet:"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#: code:addons/base_import/static/src/import_data_options/import_data_options.js:0
#, python-format
msgid "Skip record"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Space"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Standard Fields"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Start at line"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "Stop Import"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.js:0
#, python-format
msgid "Suggested Fields"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Tab"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Test"
msgstr "Teste"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.js:0
#, python-format
msgid "Testing"
msgstr "Testes"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Text Delimiter:"
msgstr "Delimitador de texto:"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "The file contains blocking errors (see below)"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "The file will be imported by batches"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "This column will be concatenated in field"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "Thousands Separator:"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "To import multiple values, separate them by a comma."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "To import, select a field..."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Track history during import"
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "Unable to load \"{extension}\" file: requires Python module \"{modname}\""
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"Unsupported file format \"{}\", import only supports CSV, ODS, XLS and XLSX"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_content/import_data_content.xml:0
#, python-format
msgid "Untitled"
msgstr "Sem título"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Upload File"
msgstr "Enviar Ficheiro"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_action/import_action.xml:0
#, python-format
msgid "Upload an Excel or CSV file to import"
msgstr "Envie um ficheiro CSV ou do Excel para importar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid ""
"Use HH for hours in a 24h system, use II in conjonction with 'p' for a 12h "
"system. You can use a custom format in addition to the suggestions provided."
" Leave empty to let Odoo guess the format (recommended)"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid ""
"Use YYYY to represent the year, MM for the month and DD for the day. Include"
" separators such as a dot, forward slash or dash. You can use a custom "
"format in addition to the suggestions provided. Leave empty to let Odoo "
"guess the format (recommended)"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid "Use first row as header"
msgstr ""

#. module: base_import
#: model:ir.model,name:base_import.model_res_users
msgid "User"
msgstr "Utilizador"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_sidepanel/import_data_sidepanel.xml:0
#, python-format
msgid ""
"Warning: ignores the labels line, empty lines and lines composed only of "
"empty cells"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_options/import_data_options.xml:0
#, python-format
msgid "When a value cannot be matched:"
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid ""
"You can not import images via URL, check with your administrator or support "
"for the reason."
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_model.js:0
#, python-format
msgid "You can test or reload your file before resuming the import."
msgstr ""

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "You must configure at least one field to import"
msgstr "Deve configurar pelo menos um campo a importar"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "at multiple rows"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "at row"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "in field"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "minutes"
msgstr "minutos"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_column_error/import_data_column_error.xml:0
#, python-format
msgid "more"
msgstr "mais"

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "out of"
msgstr ""

#. module: base_import
#. odoo-javascript
#: code:addons/base_import/static/src/import_data_progress/import_data_progress.xml:0
#, python-format
msgid "seconds"
msgstr "segundos"

#. module: base_import
#. odoo-python
#: code:addons/base_import/models/base_import.py:0
#, python-format
msgid "unknown error code %s"
msgstr ""
