.o_import_data_content {
    .o_import_field_icon {
        filter: invert(0.5); // make the icons visible in dark theme
        background-image: url('/base_import/static/src/import_data_content/img/studio_icons.png');
        background-repeat: no-repeat;
        width: 25px;
        height: 20px;
    }

    .o_import_file_column_cell {
        max-width: 300px;
    }

    .o_import_preview {
        width: fit-content;
    }

    td {
        vertical-align: top;
    }
}

@mixin o-import-sprite-icon($x: 0, $y: 0) {
    background-position: $x $y;
}

.o_import_field_icon_binary {
    @include o-import-sprite-icon(0, 0);
}

.o_import_field_icon_boolean {
    @include o-import-sprite-icon(0, -20px);
}

.o_import_field_icon_char {
    @include o-import-sprite-icon(0, -40px);
}

.o_import_field_icon_date {
    @include o-import-sprite-icon(0, -80px);
}

.o_import_field_icon_datetime {
    @include o-import-sprite-icon(0, -100px);
}

.o_import_field_icon_float {
    @include o-import-sprite-icon(0, -120px);
}

.o_import_field_icon_html {
    @include o-import-sprite-icon(0, -140px);
}

.o_import_field_icon_integer {
    @include o-import-sprite-icon(0, -180px);
}

.o_import_field_icon_many2many {
    @include o-import-sprite-icon(0, -200px);
}

.o_import_field_icon_many2one {
    @include o-import-sprite-icon(0, -220px);
}

.o_import_field_icon_one2many {
    @include o-import-sprite-icon(0, -260px);
}

.o_import_field_icon_selection {
    @include o-import-sprite-icon(0, -280px);
}

.o_import_field_icon_text {
    @include o-import-sprite-icon(0, -340px);
}
