# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_qr_code_emv
# 
# Translators:
# jonasyng<PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Icelandic (https://app.transifex.com/odoo/teams/41243/is/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: is\n"
"Plural-Forms: nplurals=2; plural=(n % 10 != 1 || n % 100 == 11);\n"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid "A bank account is required for EMV QR Code generation."
msgstr ""

#. module: account_qr_code_emv
#: model:ir.model,name:account_qr_code_emv.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Bankareikningar"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__country_code
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__country_code
msgid "Country Code"
msgstr "Landskóði"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__display_qr_setting
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__display_qr_setting
msgid "Display Qr Setting"
msgstr "Sýna Qr stillingu"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid "EMV Merchant-Presented QR-code"
msgstr "EMV Merchant-Presented QR-kóði"

#. module: account_qr_code_emv
#: model_terms:ir.ui.view,arch_db:account_qr_code_emv.view_partner_bank_form_inherit_account
msgid "EMV QR Configuration"
msgstr "EMV QR stillingar"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__include_reference
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__include_reference
msgid "Include Reference"
msgstr "Hafa tilvísun"

#. module: account_qr_code_emv
#: model:ir.model.fields,help:account_qr_code_emv.field_account_setup_bank_manual_config__include_reference
#: model:ir.model.fields,help:account_qr_code_emv.field_res_partner_bank__include_reference
msgid "Include the reference in the QR code."
msgstr "Láttu tilvísunina fylgja með QR kóðanum."

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid "Missing Merchant Account Information."
msgstr "Vantar upplýsingar um söluaðilareikning."

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid "Missing Merchant City."
msgstr "Vantar kaupmannaborg."

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid "Missing Proxy Type."
msgstr "Vantar proxy-tegund."

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid "Missing Proxy Value."
msgstr "Vantar umboðsgildi."

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid ""
"No EMV QR Code is available for the country of the account "
"%(account_number)s."
msgstr ""
"Enginn EMV QR kóða er tiltækur fyrir landið þar sem reikningurinn er "
"%(account_number)s."

#. module: account_qr_code_emv
#: model:ir.model.fields.selection,name:account_qr_code_emv.selection__res_partner_bank__proxy_type__none
msgid "None"
msgstr "Enginn"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__proxy_type
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__proxy_type
msgid "Proxy Type"
msgstr "Tegund proxy"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__proxy_value
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__proxy_value
msgid "Proxy Value"
msgstr "Gildi proxy"

#. module: account_qr_code_emv
#: model:ir.model.fields,help:account_qr_code_emv.field_account_setup_bank_manual_config__country_code
#: model:ir.model.fields,help:account_qr_code_emv.field_res_partner_bank__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO landskóði í tveimur stöfum. \n"
"Þú getur notað þennan reit fyrir skjóta leit."
