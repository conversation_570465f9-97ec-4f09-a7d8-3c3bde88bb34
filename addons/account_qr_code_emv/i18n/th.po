# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_qr_code_emv
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Rasaree<PERSON>ppiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid "A bank account is required for EMV QR Code generation."
msgstr "จำเป็นต้องมีบัญชีธนาคารสำหรับการสร้าง EMV QR โค้ด"

#. module: account_qr_code_emv
#: model:ir.model,name:account_qr_code_emv.model_res_partner_bank
msgid "Bank Accounts"
msgstr "บัญชีธนาคาร"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__country_code
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__country_code
msgid "Country Code"
msgstr "รหัสประเทศ"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__display_qr_setting
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__display_qr_setting
msgid "Display Qr Setting"
msgstr "แสดงการตั้งค่า Qr"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid "EMV Merchant-Presented QR-code"
msgstr "รหัส QR ที่นำเสนอโดยผู้ค้า EMV"

#. module: account_qr_code_emv
#: model_terms:ir.ui.view,arch_db:account_qr_code_emv.view_partner_bank_form_inherit_account
msgid "EMV QR Configuration"
msgstr "การกำหนดค่า EMV QR"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__include_reference
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__include_reference
msgid "Include Reference"
msgstr "รวมการอ้างอิง"

#. module: account_qr_code_emv
#: model:ir.model.fields,help:account_qr_code_emv.field_account_setup_bank_manual_config__include_reference
#: model:ir.model.fields,help:account_qr_code_emv.field_res_partner_bank__include_reference
msgid "Include the reference in the QR code."
msgstr "รวมข้อมูลอ้างอิงในโค้ด QR"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid "Missing Merchant Account Information."
msgstr "ข้อมูลบัญชีของผู้ค้าขาดหายไป"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid "Missing Merchant City."
msgstr "เมืองของผู้ค้าขาดหายไป"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid "Missing Proxy Type."
msgstr "ไม่มีประเภทพร็อกซี"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid "Missing Proxy Value."
msgstr "ไม่มีค่าพร็อกซี"

#. module: account_qr_code_emv
#. odoo-python
#: code:addons/account_qr_code_emv/models/res_bank.py:0
#, python-format
msgid ""
"No EMV QR Code is available for the country of the account "
"%(account_number)s."
msgstr "ไม่มีรหัส QR ของ EMV สำหรับประเทศของบัญชี %(account_number)s"

#. module: account_qr_code_emv
#: model:ir.model.fields.selection,name:account_qr_code_emv.selection__res_partner_bank__proxy_type__none
msgid "None"
msgstr "ไม่มี"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__proxy_type
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__proxy_type
msgid "Proxy Type"
msgstr "ประเภทพร็อกซี"

#. module: account_qr_code_emv
#: model:ir.model.fields,field_description:account_qr_code_emv.field_account_setup_bank_manual_config__proxy_value
#: model:ir.model.fields,field_description:account_qr_code_emv.field_res_partner_bank__proxy_value
msgid "Proxy Value"
msgstr "ค่าพร็อกซี"

#. module: account_qr_code_emv
#: model:ir.model.fields,help:account_qr_code_emv.field_account_setup_bank_manual_config__country_code
#: model:ir.model.fields,help:account_qr_code_emv.field_res_partner_bank__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"โค้ดประเทศ ISO ในสองตัวอักษร\n"
"คุณสามารถใช้ช่องนี้เพื่อการค้นหาอย่างรวดเร็ว"
