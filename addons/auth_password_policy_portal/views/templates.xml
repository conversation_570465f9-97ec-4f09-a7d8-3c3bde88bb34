<odoo>
    <template id="portal_my_security" inherit_id="portal.portal_my_security"
              name="Password policy data for portal">
        <xpath expr="//div[input[@name='new1']]" position="attributes">
            <attribute name="id">new-password-group</attribute>
        </xpath>
        <!-- only put meter on first "new password" field since both must be identical -->
        <xpath expr="//input[@name='new1']" position="after">
            <owl-component name="password_meter" props='{"selector": "input[name=new1]"}'/>
        </xpath>
    </template>
</odoo>
