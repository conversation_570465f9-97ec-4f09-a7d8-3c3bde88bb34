# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# krnkris, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Tamás <PERSON>, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/controllers/home.py:0
#, python-format
msgid "%(browser)s on %(platform)s"
msgstr "%(browser)s %(platform)splatformon"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_wizard
msgid "2-Factor Setup Wizard"
msgstr "Kétlépcsős azonosítás varázsló"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "2-Factor authentication is now enabled."
msgstr "Kétlépcsős azonosítás bekapcsolva."

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>\n"
"                                    Learn More"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>\n"
"                            Learn More"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"d-md-none d-block\">Or install an authenticator app</span>\n"
"                                        <span class=\"d-none d-md-block\">Install an authenticator app on your mobile device</span>"
msgstr ""
"<span class=\"d-md-none d-block\">Vagy telepítsen egy autentikátor alkalmazást</span>\n"
"                                        <span class=\"d-none d-md-block\">Autentikátor alkalmazás telepítése mobileszközre</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"d-none d-md-block\">When requested to do so, scan the barcode below</span>\n"
"                                    <span class=\"d-block d-md-none\">When requested to do so, copy the key below</span>"
msgstr ""
"<span class=\"d-none d-md-block\">Kérésre szkennelje be az alábbi vonalkódot</span>\n"
"                                    <span class=\"d-block d-md-none\">Kérdésre gépelje be a az alábbi kódot</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"text-muted\">Popular ones include Authy, Google Authenticator "
"or the Microsoft Authenticator.</span>"
msgstr ""
"Népszerű alkalmazások az Authy, a Google Authenticator és a Microsoft "
"Authenticator"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"<span invisible=\"not totp_enabled\" class=\"text-muted\">This account is "
"protected!</span>"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"<span invisible=\"not totp_enabled\" class=\"text-muted\">Your account is "
"protected!</span>"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Account Security"
msgstr "Jelszó kezelése"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Activate"
msgstr "Aktivál"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Added On"
msgstr "Hozzáadva"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"Are you sure? The user may be asked to enter two-factor codes again on those"
" devices"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"Are you sure? You may be asked to enter two-factor codes again on those "
"devices"
msgstr ""

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Authentication Code"
msgstr "Hitelesítő kód"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_device
msgid "Authentication Device"
msgstr "Hitelesítő eszköz"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Authenticator App Setup"
msgstr "Autentikátor alkalmazás beállítása"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cancel"
msgstr "Töröl"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cannot scan it?"
msgstr "Nem tudja beszkennelni?"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Click on this link to open your authenticator app"
msgstr "Kattintson erre a linkre az autentikátor alkalmazás megnyitásához"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__create_date
msgid "Creation Date"
msgstr "Létrehozás dátuma"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__name
msgid "Description"
msgstr "Leírás"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Device"
msgstr "Eszköz"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Disable 2FA"
msgstr "Kétlépcsős azonosítás kikapcsolása"

#. module: auth_totp
#: model:ir.actions.server,name:auth_totp.action_disable_totp
msgid "Disable two-factor authentication"
msgstr "Kétlépcsős azonosítás kikapcsolása"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__display_name
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__display_name
msgid "Display Name"
msgstr "Megjelenített név"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Don't ask again on this device"
msgstr "Ne kérje többet ezen az eszközön"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Enable 2FA"
msgstr "Kétlépcsős azonosítás bekapcsolása"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Enter your six-digit code below"
msgstr "Adja meg a hatjegyű kódot"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP irányítás"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__id
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__id
msgid "ID"
msgstr "Azonosító"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/controllers/home.py:0
#, python-format
msgid "Invalid authentication code format."
msgstr "Hitelesítő kód formátuma érvénytelen."

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_date
msgid "Last Updated on"
msgstr "Frissítve"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Learn More"
msgstr "Tudjon meg többet"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Log in"
msgstr "Bejelentkezés"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Look for an \"Add an account\" button"
msgstr "Keresse a \"Fiók hozzáadása\" gombot"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "On Apple Store"
msgstr "Az App Store-ban"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "On Google Play"
msgstr "A Google Play-en"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__qrcode
msgid "Qrcode"
msgstr "QR-kód"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Revoke"
msgstr "Visszavonás"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Revoke All"
msgstr "Összes visszavonása"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__scope
msgid "Scope"
msgstr "Hatáskör"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__secret
msgid "Secret"
msgstr "Titok"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "The verification code should only contain numbers"
msgstr "A hitelesítő kód csak számokat tartalmazhat"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid ""
"To login, enter below the six-digit authentication code provided by your Authenticator app.\n"
"                        <br/>"
msgstr ""
"A belépéshez adja meg az Autentikátor alkalmazás által generált 6-jegyű "
"belépési kódot."

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_secret
msgid "Totp Secret"
msgstr "Totp titok"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_trusted_device_ids
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Trusted Devices"
msgstr "Megbízható eszköz"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-Factor Authentication Activation"
msgstr "Kétlépcsős azonosítás aktiválása"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Two-factor Authentication"
msgstr "Kétlépcsős azonosítás"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"                                The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"                                Popular ones include Authy, Google Authenticator or the Microsoft Authenticator."
msgstr ""
"A kétlépcsős azonosítás (\"2FA\") egy kettős azonosítási rendszer.\n"
"                                Első lépésben a felhasználó a saját jelszavával jelentkezik be, majd második lépésben a mobil eszközére telepített alkalmazás által generált kódot kell megadnia.\n"
"                                Népszerű alkalmazások az Authy, a Google Authenticator és a Microsoft Authenticator."

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"                        The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"                        Popular ones include Authy, Google Authenticator or the Microsoft Authenticator."
msgstr ""

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_enabled
msgid "Two-factor authentication"
msgstr "Kétlépcsős azonosítás"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.res_users_view_search
msgid "Two-factor authentication Disabled"
msgstr "Kétlépcsős azonosítás kikapcsolva"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.res_users_view_search
msgid "Two-factor authentication Enabled"
msgstr "Kétlépcsős azonosítás bekapcsolva"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication already enabled"
msgstr "Kétlépcsős azonosítás már engedélyezve van"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication can only be enabled for yourself"
msgstr "A kétlépcsős azonosítást csak saját magának kapcsolhatja be"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication disabled for the following user(s): %s"
msgstr "A kétlépcsős azonosítás kikapcsolva ezeneknél a felhasználóknál: %s"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__url
msgid "Url"
msgstr "Webcím"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_res_users
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__user_id
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__user_id
msgid "User"
msgstr "Felhasználó"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__code
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Verification Code"
msgstr "Ellenőrző kód"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "Verification failed, please double-check the 6-digit code"
msgstr "Az ellenőrzés sikertelen, ellenőrizze a 6 számjegyzű kódot"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "e.g. 123456"
msgstr "pl.: 123456"
