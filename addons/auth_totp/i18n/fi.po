# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_totp
# 
# Translators:
# LINUX-SAUNA, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <ka<PERSON><PERSON><PERSON><PERSON><PERSON>@emsystems.fi>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <ossi.manty<PERSON><EMAIL>>, 2023\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/controllers/home.py:0
#, python-format
msgid "%(browser)s on %(platform)s"
msgstr "Selain %(browser)s käyttöjärjestelmässä %(platform)s"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_wizard
msgid "2-Factor Setup Wizard"
msgstr "Kaksivaiheisen tunnistautumisen käyttöönotto"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "2-Factor authentication is now enabled."
msgstr "Kaksivaiheinen tunnistautuminen otettu käyttöön onnistuneesti."

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>\n"
"                                    Learn More"
msgstr ""
"<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>\n"
"                                    Lue lisää"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>\n"
"                            Learn More"
msgstr ""
"<i title=\"Documentation\" class=\"fa fa-fw o_button_icon fa-info-circle\"/>\n"
"                            Lue lisää"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"d-md-none d-block\">Or install an authenticator app</span>\n"
"                                        <span class=\"d-none d-md-block\">Install an authenticator app on your mobile device</span>"
msgstr ""
"<span class=\"d-md-none d-block\">Tai asenna vahvistussovellus</span>\n"
"                                        <span class=\"d-none d-md-block\">Asenna vahvistussovellus mobiililaitteeseesi</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"d-none d-md-block\">When requested to do so, scan the barcode below</span>\n"
"                                    <span class=\"d-block d-md-none\">When requested to do so, copy the key below</span>"
msgstr ""
"<span class=\"d-none d-md-block\">Skannaa allaoleva QR-koodi sovelluksen pyytäessä sitä</span>\n"
"                                    <span class=\"d-block d-md-none\">Kopioi alla oleva koodi sovelluksen pyytäessä sitä</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid ""
"<span class=\"text-muted\">Popular ones include Authy, Google Authenticator "
"or the Microsoft Authenticator.</span>"
msgstr ""
"<span class=\"text-muted\">Suosittuja sovelluksia ovat esimerkiksi Microsoft"
" Authenticator, Authy tai Google Authenticator</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"<span invisible=\"not totp_enabled\" class=\"text-muted\">This account is "
"protected!</span>"
msgstr ""
"<span invisible=\"not totp_enabled\" class=\"text-muted\">Tämä tili on "
"suojattu!</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"<span invisible=\"not totp_enabled\" class=\"text-muted\">Your account is "
"protected!</span>"
msgstr ""
"<span invisible=\"not totp_enabled\" class=\"text-muted\">Tilisi on "
"suojattu!</span>"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Account Security"
msgstr "Tilin tietoturva"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Activate"
msgstr "Aktivoi"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Added On"
msgstr "Lisätty"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"Are you sure? The user may be asked to enter two-factor codes again on those"
" devices"
msgstr ""
"Oletko varma? Käyttäjä saattaa joutua syöttämään vahvistuskoodin uudelleen "
"laitteessaan"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"Are you sure? You may be asked to enter two-factor codes again on those "
"devices"
msgstr ""
"Oletko varma? Saatat joutua syöttämään vahvistuskoodin uudelleen "
"laitteessasi"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Authentication Code"
msgstr "Vahvistuskoodi"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_auth_totp_device
msgid "Authentication Device"
msgstr "Vahvistuslaite"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Authenticator App Setup"
msgstr "Vahvistussovelluksen käyttöönotto"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cancel"
msgstr "Peruuta"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Cannot scan it?"
msgstr "Etkö saa skannattua QR-koodia?"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Click on this link to open your authenticator app"
msgstr "Klikkaa tästä avataksesi vahvistussovelluksesi"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__create_date
msgid "Created on"
msgstr "Luotu"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__create_date
msgid "Creation Date"
msgstr "Luontipäivä"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__name
msgid "Description"
msgstr "Kuvaus"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Device"
msgstr "Laite"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Disable 2FA"
msgstr "Poista kaksivaiheinen tunnistautuminen käytöstä"

#. module: auth_totp
#: model:ir.actions.server,name:auth_totp.action_disable_totp
msgid "Disable two-factor authentication"
msgstr "Poista kaksivaiheinen tunnistautuminen käytöstä"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__display_name
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Don't ask again on this device"
msgstr "Älä kysy uudestaan tällä laitteella"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Enable 2FA"
msgstr "Ota kaksivaiheinen tunnistautuminen käyttöön"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Enter your six-digit code below"
msgstr "Kirjoita kuusinumeroinen koodi alle"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-reititys"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__id
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__id
msgid "ID"
msgstr "ID"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/controllers/home.py:0
#, python-format
msgid "Invalid authentication code format."
msgstr "Virheellinen autentikointikoodi."

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Learn More"
msgstr "Lue lisää"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid "Log in"
msgstr "Kirjaudu sisään"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Look for an \"Add an account\" button"
msgstr "Etsi \"Lisää tili\"-painike"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "On Apple Store"
msgstr "Apple App Storessa"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "On Google Play"
msgstr "Google Playssä"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__qrcode
msgid "Qrcode"
msgstr "QR-koodi"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Revoke"
msgstr "Peruuta valtuutus"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Revoke All"
msgstr "Evää kaikki"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__scope
msgid "Scope"
msgstr "Laajuus"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__secret
msgid "Secret"
msgstr "Salausavain"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "The verification code should only contain numbers"
msgstr "Vahvistuskoodissa tulee olla pekkiä numeroita"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
msgid ""
"To login, enter below the six-digit authentication code provided by your Authenticator app.\n"
"                        <br/>"
msgstr ""
"Kirjautuaksesi sisään, syötä alle kuusinumeroinen vahvistuskoodi vahvistussovelluksestasi.\n"
"                        <br/>"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_secret
msgid "Totp Secret"
msgstr "TOTP-salausavain"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_trusted_device_ids
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Trusted Devices"
msgstr "Luotetut laitteet"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-Factor Authentication Activation"
msgstr "Kaksivaiheisen tunnistautumisen aktivointi"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid "Two-factor Authentication"
msgstr "Kaksivaiheinen tunnistautuminen"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_form
msgid ""
"Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"                                The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"                                Popular ones include Authy, Google Authenticator or the Microsoft Authenticator."
msgstr ""
"Kaksivaiheinen tunnistautuminen (\"2FA\") on tapa tunnistautua kahdesti.\n"
"Kirjautuessa syötetään salasanan lisäksi koodi vahvistussovelluksesta.\n"
"Suosittuja sovelluksia ovat esimerkiksi Microsoft Authenticator, Authy sekä Google Authenticator."

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_field
msgid ""
"Two-factor Authentication (\"2FA\") is a system of double authentication.\n"
"                        The first one is done with your password and the second one with a code you get from a dedicated mobile app.\n"
"                        Popular ones include Authy, Google Authenticator or the Microsoft Authenticator."
msgstr ""
"Kaksivaiheinen tunnistautuminen (\"2FA\") on tapa tunnistautua kahdesti.\n"
"Kirjautuessa syötetään salasanan lisäksi koodi vahvistussovelluksesta.\n"
"Suosittuja sovelluksia ovat esimerkiksi Microsoft Authenticator, Authy sekä Google Authenticator."

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_res_users__totp_enabled
msgid "Two-factor authentication"
msgstr "2-vaiheinen todennus"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.res_users_view_search
msgid "Two-factor authentication Disabled"
msgstr "Kaksivaiheinen tunnistautuminen pois käytöstä"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.res_users_view_search
msgid "Two-factor authentication Enabled"
msgstr "Kaksivaiheinen tunnistautuminen käytössä"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication already enabled"
msgstr "Kaksivaiheinen tunnistautuminen on jo käytössä"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication can only be enabled for yourself"
msgstr "Voit ottaa kaksivaiheisen tunnistautumisen käyttöön vain itsellesi"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#, python-format
msgid "Two-factor authentication disabled for the following user(s): %s"
msgstr ""
"Kaksivaiheinen tunnistautuminen on poissa käytöstä seuraavilla käyttäjillä: "
"%s"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__url
msgid "Url"
msgstr "URL"

#. module: auth_totp
#: model:ir.model,name:auth_totp.model_res_users
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_device__user_id
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__user_id
msgid "User"
msgstr "Käyttäjä"

#. module: auth_totp
#: model:ir.model.fields,field_description:auth_totp.field_auth_totp_wizard__code
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "Verification Code"
msgstr "Vahvistuskoodi"

#. module: auth_totp
#. odoo-python
#: code:addons/auth_totp/models/res_users.py:0
#: code:addons/auth_totp/wizard/auth_totp_wizard.py:0
#, python-format
msgid "Verification failed, please double-check the 6-digit code"
msgstr "Vahvistus epäonnistui, tarkista kuusinumeroinen koodi"

#. module: auth_totp
#: model_terms:ir.ui.view,arch_db:auth_totp.auth_totp_form
#: model_terms:ir.ui.view,arch_db:auth_totp.view_totp_wizard
msgid "e.g. 123456"
msgstr "esim. 123456"
