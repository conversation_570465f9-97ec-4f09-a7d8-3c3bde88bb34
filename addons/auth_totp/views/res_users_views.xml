<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.ui.view" id="res_users_view_search">
        <field name="name">res.users.view.search.inherit.auth.totp</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_search" />
        <field name="arch" type="xml">
            <xpath expr="//search" position="inside">
                <separator/>
                <filter name="totp_enabled" string="Two-factor authentication Enabled" domain="[('totp_enabled','!=',False)]"/>
                <filter name="totp_disabled" string="Two-factor authentication Disabled" domain="[('totp_enabled','=',False)]"/>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="view_totp_form">
        <field name="name">user form: add totp status</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='preferences']" position="after">
                <page string="Account Security" name="security" invisible="not id">
                    <field name="totp_enabled" invisible="1"/>
                    <!-- For own user, allow to activate the two-factor Authentication -->
                    <div>
                        <div class="o_horizontal_separator d-flex align-items-center mt-2 mb-4 text-uppercase fw-bolder small">Two-factor Authentication
                                <div invisible="totp_enabled">
                                    <button invisible="id == uid" name="action_totp_enable_wizard"
                                            disabled="1" type="object" class="fa fa-toggle-off o_auth_2fa_btn disabled" aria-label="Enable 2FA"></button>
                                            <button invisible="id != uid" name="action_totp_enable_wizard"
                                            type="object" class="fa fa-toggle-off o_auth_2fa_btn disabled pe-auto" aria-label="Enable 2FA"></button>
                                </div>
                                <button invisible="not totp_enabled" name="action_totp_disable" type="object"
                                class="fa fa-toggle-on o_auth_2fa_btn text-primary enabled" aria-label="Disable 2FA"></button>
                            </div>
                            <span invisible="totp_enabled" class="text-muted">
                                Two-factor Authentication ("2FA") is a system of double authentication.
                                The first one is done with your password and the second one with a code you get from a dedicated mobile app.
                                Popular ones include Authy, Google Authenticator or the Microsoft Authenticator.
                                <a href="https://www.odoo.com/documentation/17.0/applications/general/auth/2fa.html" title="Learn More" target="_blank">
                                    <i title="Documentation" class="fa fa-fw o_button_icon fa-info-circle"></i>
                                    Learn More
                                </a>
                                </span>
                                <span invisible="not totp_enabled" class="text-muted">This account is protected!</span>
                            </div>
                        <group name="auth_devices" string="Trusted Devices" invisible="not totp_trusted_device_ids">
                            <div colspan="2">
                                <field name="totp_trusted_device_ids" nolabel="1" colspan="4" readonly="1">
                                    <tree create="false" delete="false">
                                        <field name="name" string="Device"/>
                                        <field name="create_date" string="Added On"/>
                                        <button type="object" name="remove"
                                                title="Revoke" icon="fa-trash"/>
                                    </tree>
                                </field>
                                <button name="revoke_all_devices" string="Revoke All" type="object" class="btn btn-secondary"
                                        confirm="Are you sure? The user may be asked to enter two-factor codes again on those devices"/>
                            </div>
                        </group>
                </page>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="view_totp_field">
        <field name="name">users preference: totp</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form_simple_modif"/>
        <field name="arch" type="xml">
            <group name="auth" position="after">
                <field name="totp_enabled" invisible="1"/>
                <div>
                    <div class="o_horizontal_separator mt-2 mb-4 text-uppercase fw-bolder small">Two-factor Authentication
                        <button invisible="totp_enabled" name="action_totp_enable_wizard"
                            type="object" class="fa fa-toggle-off o_auth_2fa_btn" aria-label="Enable 2FA"/>
                        <button invisible="not totp_enabled" name="action_totp_disable"
                            type="object" class="fa fa-toggle-on o_auth_2fa_btn text-primary" aria-label="Disable 2FA"/>
                    </div>
                    <span invisible="not totp_enabled" class="text-muted">Your account is protected!</span>
                    <span invisible="totp_enabled" class="text-muted">
                        Two-factor Authentication ("2FA") is a system of double authentication.
                        The first one is done with your password and the second one with a code you get from a dedicated mobile app.
                        Popular ones include Authy, Google Authenticator or the Microsoft Authenticator.
                        <a href="https://www.odoo.com/documentation/17.0/applications/general/auth/2fa.html" title="Learn More" target="_blank">
                            <i title="Documentation" class="fa fa-fw o_button_icon fa-info-circle"></i>
                            Learn More
                        </a>
                    </span>
                    <group name="auth_devices" string="Trusted Devices" invisible="not totp_trusted_device_ids">
                        <div colspan="2">
                            <field name="totp_trusted_device_ids" nolabel="1" colspan="4" readonly="1">
                                <tree create="false" delete="false">
                                    <field name="name" string="Device"/>
                                    <field name="create_date" string="Added On"/>
                                    <button type="object" name="remove"
                                            title="Revoke" icon="fa-trash"/>
                                </tree>
                            </field>
                            <button name="revoke_all_devices" string="Revoke All" type="object" class="btn btn-secondary"
                                    confirm="Are you sure? You may be asked to enter two-factor codes again on those devices"/>
                        </div>
                    </group>
                </div>
            </group>
        </field>
    </record>
</odoo>
