<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base_module_install_request_view_form" model="ir.ui.view">
        <field name="name">base.module.install.request.view.form</field>
        <field name="model">base.module.install.request</field>
        <field name="arch" type="xml">
            <form>
                <div class="alert alert-warning oe_button_box" role="alert">
                    <p class="mt-3">
                        This app is included in your subscription. It's free to activate, but only an administrator can do it. Fill this form to send an activation request.
                    </p>
                </div>
                <field name="module_id" invisible="1"/>
                <group string="Send to:">
                    <field colspan="2" name="user_ids" widget="many2many_tags" nolabel="1"/>
                </group>
                <group string="Why do you need this module?">
                    <field colspan="2" name="body_html" widget="html" nolabel="1" placeholder="e.g. I'd like to use the SMS Marketing module to organize the promotion of our internal events, and exhibitions. I need access for 3 people of my team."/>
                </group>
                <footer>
                    <button string="Request Activation" class="btn-primary" type="object" name="action_send_request" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="x"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="base_module_install_review_view_form" model="ir.ui.view">
        <field name="name">base.module.install.review.view.form</field>
        <field name="model">base.module.install.review</field>
        <field name="arch" type="xml">
            <form>
                <div class="alert alert-success oe_button_box" role="alert">
                    <p class="mt-3">
                        No extra cost, this application is free.
                    </p>
                </div>
                <field name="module_id" invisible="1"/>
                <field name="module_ids" widget="many2many_tags" invisible="1"/>
                <field name="modules_description"/>
                <footer>
                    <button string="Install App" class="btn-primary" type="object" name="action_install_module" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="x"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="action_base_module_install_review" model="ir.actions.act_window">
        <field name="name">You are about to install an extra application</field>
        <field name="res_model">base.module.install.review</field>
        <field name="view_mode">form</field>
        <field name="context">{ 'default_module_id': active_id }</field>
        <field name="target">new</field>
    </record>
</odoo>
