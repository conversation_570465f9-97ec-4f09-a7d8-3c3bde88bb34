# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_install_request
# 
# Translators:
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2023
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: base_install_request
#: model:mail.template,body_html:base_install_request.mail_template_base_install_request
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello,\n"
"        <br><br>\n"
"        <span style=\"font-weight: bold;\" t-out=\"object.user_id.name\"></span> has requested to activate the <span style=\"font-weight: bold;\" t-out=\"object.module_id.shortdesc\"></span> module. This module is included in your subscription. It has <span style=\"color: #875A7B; font-weight: bold;\">no extra cost</span>, but an administrator role is required to activate it.\n"
"        <br><br>\n"
"        <blockquote>\n"
"            <t t-out=\"object.body_html\"></t>\n"
"        </blockquote>\n"
"        <br><br>\n"
"        <a style=\"background-color:#875A7B; padding:8px 16px 8px 16px; text-decoration:none; color:#fff; border-radius:5px\" t-attf-href=\"/web?#action=base_install_request.action_base_module_install_review&amp;active_id={{ object.module_id.id }}&amp;menu_id={{ ctx['menu_id'] }}\">Review Request</a>\n"
"        <br><br>\n"
"        Thanks,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Вітаємо,\n"
"        <br/><br/>\n"
"        <span style=\"font-weight: bold;\" t-out=\"object.user_id.name\"/> є запит на активацію <span style=\"font-weight: bold;\" t-out=\"object.module_id.shortdesc\"/> модуля. Цей модулю включений у вашу підписку. Він не має <span style=\"color: #875A7B; font-weight: bold;\">додаткової вартості</span>, але для його активації потрібні права адміністратора.\n"
"        <br/><br/>\n"
"        <blockquote>\n"
"            <t t-out=\"object.body_html\"/>\n"
"        </blockquote>\n"
"        <br/><br/>\n"
"        <a style=\"background-color:#875A7B; padding:8px 16px 8px 16px; text-decoration:none; color:#fff; border-radius:5px\" t-attf-href=\"/web?#action=base_install_request.action_base_module_install_review&amp;active_id={{ object.module_id.id }}&amp;menu_id={{ ctx['menu_id'] }}\">Переглянути запит</a>\n"
"        <br/><br/>\n"
"        Дякуємо,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br/><br/>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br/>Mitchell Admin</t>\n"
"        </t>\n"
"        <br/><br/>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/models/ir_module_module.py:0
#, python-format
msgid "Activation Request of \"%s\""
msgstr "Запит на активацію \"%s\""

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__body_html
msgid "Body"
msgstr "Тіло листа"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "Cancel"
msgstr "Скасувати"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__create_uid
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__create_uid
msgid "Created by"
msgstr "Створив"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__create_date
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__create_date
msgid "Created on"
msgstr "Створено"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__module_ids
msgid "Depending Apps"
msgstr "Залежні модулі"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__display_name
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__id
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__id
msgid "ID"
msgstr "ID"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "Install App"
msgstr "Встановити модуль"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__write_uid
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__write_date
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: base_install_request
#: model:mail.template,name:base_install_request.mail_template_base_install_request
msgid "Mail: Install Request"
msgstr "Пошти: Запит на встановлення"

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_ir_module_module
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__module_id
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__module_id
msgid "Module"
msgstr "Модуль"

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_base_module_install_request
msgid "Module Activation Request"
msgstr "Запит на активацію модуля"

#. module: base_install_request
#: model:mail.template,subject:base_install_request.mail_template_base_install_request
msgid "Module Activation Request for \"{{ object.module_id.shortdesc }}\""
msgstr "Запит на активацію модуля для \"{{ object.module_id.shortdesc }}\""

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_base_module_install_review
msgid "Module Activation Review"
msgstr "Огляд активації модуля"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__modules_description
msgid "Modules Description"
msgstr "Опис модулів"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "No extra cost, this application is free."
msgstr "Немає додаткової вартості, цей модуль безкоштовний."

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
#, python-format
msgid "No module selected."
msgstr "Не вибрано модулів."

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.ir_module_module_view_kanban
msgid "Request Access"
msgstr "Доступ до запитів"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Request Activation"
msgstr "Надіслати запит на активацію"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__user_ids
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Send to:"
msgstr "Надіслати до:"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_description
msgid "The following apps will be installed:"
msgstr "Будуть встановлені наступні модулі:"

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
#, python-format
msgid "The module is already installed."
msgstr "Модуль вже встановлено."

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid ""
"This app is included in your subscription. It's free to activate, but only "
"an administrator can do it. Fill this form to send an activation request."
msgstr ""
"Цей моудль включений у вашу підписку. Активація безкоштовна, але це може "
"зробити лише адміністратор. Заповніть цю форму, щоб надіслати запит на "
"активацію."

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__user_id
msgid "User"
msgstr "Користувач"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Why do you need this module?"
msgstr "Чому вам потрібен цей модуль?"

#. module: base_install_request
#: model:ir.actions.act_window,name:base_install_request.action_base_module_install_review
msgid "You are about to install an extra application"
msgstr "Ви збираєтеся встановити додатковий модуль"

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
#, python-format
msgid "Your request has been successfully sent"
msgstr "Ваш запит успішно надісланий"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid ""
"e.g. I'd like to use the SMS Marketing module to organize the promotion of "
"our internal events, and exhibitions. I need access for 3 people of my team."
msgstr ""
"напр. Я хотів би використовувати модуль SMS Маркетинг для організації "
"просування наших внутрішніх заходів та виставок. Мені потрібен доступ для 3 "
"людей з моєї команди."
