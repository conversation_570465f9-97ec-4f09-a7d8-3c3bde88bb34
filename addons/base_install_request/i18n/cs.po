# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_install_request
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: base_install_request
#: model:mail.template,body_html:base_install_request.mail_template_base_install_request
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello,\n"
"        <br><br>\n"
"        <span style=\"font-weight: bold;\" t-out=\"object.user_id.name\"></span> has requested to activate the <span style=\"font-weight: bold;\" t-out=\"object.module_id.shortdesc\"></span> module. This module is included in your subscription. It has <span style=\"color: #875A7B; font-weight: bold;\">no extra cost</span>, but an administrator role is required to activate it.\n"
"        <br><br>\n"
"        <blockquote>\n"
"            <t t-out=\"object.body_html\"></t>\n"
"        </blockquote>\n"
"        <br><br>\n"
"        <a style=\"background-color:#875A7B; padding:8px 16px 8px 16px; text-decoration:none; color:#fff; border-radius:5px\" t-attf-href=\"/web?#action=base_install_request.action_base_module_install_review&amp;active_id={{ object.module_id.id }}&amp;menu_id={{ ctx['menu_id'] }}\">Review Request</a>\n"
"        <br><br>\n"
"        Thanks,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hezký den,\n"
"        <br><br>\n"
"        <span style=\"font-weight: bold;\" t-out=\"object.user_id.name\"></span> požádal(a) o aktivaci modulu <span style=\"font-weight: bold;\" t-out=\"object.module_id.shortdesc\"></span>. Tento modul je součástí vašeho předplatného. Nemá žádné <span style=\"color: #875A7B; font-weight: bold;\">dodatečné náklady</span>, ale pro aktivaci je potřebná role správce.\n"
"        <br><br>\n"
"        <blockquote>\n"
"            <t t-out=\"object.body_html\"></t>\n"
"        </blockquote>\n"
"        <br><br>\n"
"        <a style=\"background-color:#875A7B; padding:8px 16px 8px 16px; text-decoration:none; color:#fff; border-radius:5px\" t-attf-href=\"/web?#action=base_install_request.action_base_module_install_review&amp;active_id={{ object.module_id.id }}&amp;menu_id={{ ctx['menu_id'] }}\">Posoudit žádost</a>\n"
"        <br><br>\n"
"        Díky,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/models/ir_module_module.py:0
#, python-format
msgid "Activation Request of \"%s\""
msgstr "Žádost o aktivaci \"%s\""

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__body_html
msgid "Body"
msgstr "Tělo"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "Cancel"
msgstr "Zrušit"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__create_uid
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__create_uid
msgid "Created by"
msgstr "Vytvořeno uživatelem"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__create_date
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__create_date
msgid "Created on"
msgstr "Vytvořeno dne"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__module_ids
msgid "Depending Apps"
msgstr "Závislé aplikace"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__display_name
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__display_name
msgid "Display Name"
msgstr "Zobrazovací název"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__id
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__id
msgid "ID"
msgstr "ID"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "Install App"
msgstr "Instalovat aplikaci"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__write_uid
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno uživatelem"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__write_date
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__write_date
msgid "Last Updated on"
msgstr "Naposledy upraveno dne"

#. module: base_install_request
#: model:mail.template,name:base_install_request.mail_template_base_install_request
msgid "Mail: Install Request"
msgstr "E-mail: Žádost o instalaci"

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_ir_module_module
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__module_id
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__module_id
msgid "Module"
msgstr "Modul"

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_base_module_install_request
msgid "Module Activation Request"
msgstr "Požadavek na aktivaci modulu"

#. module: base_install_request
#: model:mail.template,subject:base_install_request.mail_template_base_install_request
msgid "Module Activation Request for \"{{ object.module_id.shortdesc }}\""
msgstr "Požadavek na aktivaci modulu \"{{ object.module_id.shortdesc }}\""

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_base_module_install_review
msgid "Module Activation Review"
msgstr "Schválenní aktivace modulu"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__modules_description
msgid "Modules Description"
msgstr "Popis modulu"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "No extra cost, this application is free."
msgstr "Žádné další náklady, aplikace je zdarma."

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
#, python-format
msgid "No module selected."
msgstr "Není vybrán žádný modul."

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.ir_module_module_view_kanban
msgid "Request Access"
msgstr "Požádat o přístup"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Request Activation"
msgstr "Požádat o aktivaci"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__user_ids
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Send to:"
msgstr "Komu odeslat:"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_description
msgid "The following apps will be installed:"
msgstr "Budou nainstalovány následující aplikace:"

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
#, python-format
msgid "The module is already installed."
msgstr "Modul je již nainstalován."

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid ""
"This app is included in your subscription. It's free to activate, but only "
"an administrator can do it. Fill this form to send an activation request."
msgstr ""
"Tato aplikace je součástí vašeho předplatného. Aktivace je zdarma, ale "
"provést ji může pouze správce. Pro odeslání požadavku na aktivaci vyplňte "
"tento formulář."

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__user_id
msgid "User"
msgstr "Uživatel"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Why do you need this module?"
msgstr "Proč potřebujete tento modul?"

#. module: base_install_request
#: model:ir.actions.act_window,name:base_install_request.action_base_module_install_review
msgid "You are about to install an extra application"
msgstr "Chystáte se instalovat další aplikaci"

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
#, python-format
msgid "Your request has been successfully sent"
msgstr "Váš požadavek byl úspěšně odeslán"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid ""
"e.g. I'd like to use the SMS Marketing module to organize the promotion of "
"our internal events, and exhibitions. I need access for 3 people of my team."
msgstr ""
"např. Rád bych využíval modul SMS marketing pro organizaci podpory našich "
"interních událostí a výstav. Potřebuji přístup pro 3 osoby z mého týmu."
