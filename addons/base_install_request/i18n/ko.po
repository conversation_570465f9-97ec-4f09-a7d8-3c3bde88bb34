# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_install_request
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_install_request
#: model:mail.template,body_html:base_install_request.mail_template_base_install_request
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello,\n"
"        <br><br>\n"
"        <span style=\"font-weight: bold;\" t-out=\"object.user_id.name\"></span> has requested to activate the <span style=\"font-weight: bold;\" t-out=\"object.module_id.shortdesc\"></span> module. This module is included in your subscription. It has <span style=\"color: #875A7B; font-weight: bold;\">no extra cost</span>, but an administrator role is required to activate it.\n"
"        <br><br>\n"
"        <blockquote>\n"
"            <t t-out=\"object.body_html\"></t>\n"
"        </blockquote>\n"
"        <br><br>\n"
"        <a style=\"background-color:#875A7B; padding:8px 16px 8px 16px; text-decoration:none; color:#fff; border-radius:5px\" t-attf-href=\"/web?#action=base_install_request.action_base_module_install_review&amp;active_id={{ object.module_id.id }}&amp;menu_id={{ ctx['menu_id'] }}\">Review Request</a>\n"
"        <br><br>\n"
"        Thanks,\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        안녕하세요,\n"
"        <br><br>\n"
"        <span style=\"font-weight: bold;\" t-out=\"object.user_id.name\"></span>님이 <span style=\"font-weight: bold;\" t-out=\"object.module_id.shortdesc\"></span> 모듈 활성화를 요청하였습니다. 해당 모듈은 이미 고객님의 구독 항목에 포함되어 있어 <span style=\"color: #875A7B; font-weight: bold;\">추가 비용은 따로 청구되지 않으나,</span> 활성화를 위해 관리자를 지정해 주셔야 합니다.\n"
"        <br><br>\n"
"        <blockquote>\n"
"            <t t-out=\"object.body_html\"></t>\n"
"        </blockquote>\n"
"        <br><br>\n"
"        <a style=\"background-color:#875A7B; padding:8px 16px 8px 16px; text-decoration:none; color:#fff; border-radius:5px\" t-attf-href=\"/web?#action=base_install_request.action_base_module_install_review&amp;active_id={{ object.module_id.id }}&amp;menu_id={{ ctx['menu_id'] }}\">요청 검토하기</a>\n"
"        <br><br>\n"
"        감사합니다.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/models/ir_module_module.py:0
#, python-format
msgid "Activation Request of \"%s\""
msgstr "\"%s\" 활성화 요청"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__body_html
msgid "Body"
msgstr "본문"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "Cancel"
msgstr "취소"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__create_uid
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__create_uid
msgid "Created by"
msgstr "작성자"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__create_date
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__create_date
msgid "Created on"
msgstr "작성일자"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__module_ids
msgid "Depending Apps"
msgstr "종속 앱"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__display_name
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__display_name
msgid "Display Name"
msgstr "표시명"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__id
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__id
msgid "ID"
msgstr "ID"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "Install App"
msgstr "앱 설치"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__write_uid
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__write_date
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: base_install_request
#: model:mail.template,name:base_install_request.mail_template_base_install_request
msgid "Mail: Install Request"
msgstr "메일: 설치 요청"

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_ir_module_module
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__module_id
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__module_id
msgid "Module"
msgstr "모듈"

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_base_module_install_request
msgid "Module Activation Request"
msgstr "모듈 활성화 요청"

#. module: base_install_request
#: model:mail.template,subject:base_install_request.mail_template_base_install_request
msgid "Module Activation Request for \"{{ object.module_id.shortdesc }}\""
msgstr "\"{{ object.module_id.shortdesc }}\"에 대한 모듈 활성화 요청"

#. module: base_install_request
#: model:ir.model,name:base_install_request.model_base_module_install_review
msgid "Module Activation Review"
msgstr "모듈 활성화 검토"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_review__modules_description
msgid "Modules Description"
msgstr "모듈 설명"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_view_form
msgid "No extra cost, this application is free."
msgstr "추가 비용 없이, 이 애플리케이션은 무료입니다."

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
#, python-format
msgid "No module selected."
msgstr "선택한 모듈이 없습니다."

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.ir_module_module_view_kanban
msgid "Request Access"
msgstr "액세스 권한 요청"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Request Activation"
msgstr "활성화 요청"

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__user_ids
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Send to:"
msgstr "보내기:"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_review_description
msgid "The following apps will be installed:"
msgstr "다음 앱을 설치합니다:"

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
#, python-format
msgid "The module is already installed."
msgstr "이미 설치된 모듈입니다."

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid ""
"This app is included in your subscription. It's free to activate, but only "
"an administrator can do it. Fill this form to send an activation request."
msgstr ""
"구독에 포함되어 있는 앱입니다. 무료로 활성화할 수 있으나, 관리자만 실행할 수 있습니다. 활성화 요청을 하시려면 이 양식을 작성하세요."

#. module: base_install_request
#: model:ir.model.fields,field_description:base_install_request.field_base_module_install_request__user_id
msgid "User"
msgstr "사용자"

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid "Why do you need this module?"
msgstr "이 모듈이 필요한 이유는 무엇인가요?"

#. module: base_install_request
#: model:ir.actions.act_window,name:base_install_request.action_base_module_install_review
msgid "You are about to install an extra application"
msgstr "추가 애플리케이션을 설치하려고 합니다."

#. module: base_install_request
#. odoo-python
#: code:addons/base_install_request/wizard/base_module_install_request.py:0
#, python-format
msgid "Your request has been successfully sent"
msgstr "요청을 성공적으로 전송하였습니다."

#. module: base_install_request
#: model_terms:ir.ui.view,arch_db:base_install_request.base_module_install_request_view_form
msgid ""
"e.g. I'd like to use the SMS Marketing module to organize the promotion of "
"our internal events, and exhibitions. I need access for 3 people of my team."
msgstr ""
"예: SMS 마케팅 모듈을 사용하여 내부 행사 및 전시회 홍보를 진행하고 싶습니다. 우리 팀의 3명에 대한 액세스 권한이 필요합니다."
