<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="mail_template_base_install_request" model="mail.template">
            <field name="name">Mail: Install Request</field>
            <field name="model_id" ref="base_install_request.model_base_module_install_request"/>
            <field name="subject">Module Activation Request for "{{ object.module_id.shortdesc }}"</field>
            <field name="email_from">{{ object.user_id.email_formatted or user.email_formatted }}</field>
            <field name="partner_to" >{{ ctx['partner'].id}}</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <p style="margin: 0px; padding: 0px; font-size: 13px;">
        Hello,
        <br/><br/>
        <span style="font-weight: bold;" t-out="object.user_id.name"/> has requested to activate the <span style="font-weight: bold;" t-out="object.module_id.shortdesc"/> module. This module is included in your subscription. It has <span style="color: #875A7B; font-weight: bold;">no extra cost</span>, but an administrator role is required to activate it.
        <br/><br/>
        <blockquote>
            <t t-out="object.body_html"/>
        </blockquote>
        <br/><br/>
        <a style="background-color:#875A7B; padding:8px 16px 8px 16px; text-decoration:none; color:#fff; border-radius:5px" t-attf-href="/web?#action=base_install_request.action_base_module_install_review&amp;active_id={{ object.module_id.id }}&amp;menu_id={{ ctx['menu_id'] }}">Review Request</a>
        <br/><br/>
        Thanks,
        <t t-if="not is_html_empty(object.user_id.signature)">
            <br/><br/>
            <t t-out="object.user_id.signature or ''">--<br/>Mitchell Admin</t>
        </t>
        <br/><br/>
    </p>
</div>
            </field>
            <field name="lang">{{ ctx['partner'].lang or user.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>
