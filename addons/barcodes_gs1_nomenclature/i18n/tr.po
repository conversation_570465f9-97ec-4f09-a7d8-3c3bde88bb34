# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* barcodes_gs1_nomenclature
# 
# Translators:
# <PERSON><PERSON><PERSON> YILMAZ <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Halil, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__alpha
msgid "Alpha-Numeric Name"
msgstr "Alfanümerik İsim"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_nomenclature__gs1_separator_fnc1
msgid ""
"Alternative regex delimiter for the FNC1. The separator must not match the "
"begin/end of any related rules pattern."
msgstr ""
"FNC1 için alternatif normal ifade sınırlayıcı. Ayırıcı, herhangi bir ilgili "
"kural kalıbının başlangıcı/bitişi ile eşleşmemelidir."

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__associated_uom_id
msgid "Associated Uom"
msgstr "İlişkili Ölçü Birimi"

#. module: barcodes_gs1_nomenclature
#: model:ir.model,name:barcodes_gs1_nomenclature.model_barcode_nomenclature
msgid "Barcode Nomenclature"
msgstr "Barkod Kural Seti"

#. module: barcodes_gs1_nomenclature
#: model:ir.model,name:barcodes_gs1_nomenclature.model_barcode_rule
msgid "Barcode Rule"
msgstr "Barkod kuralı"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__use_date
msgid "Best before Date"
msgstr "Tavsiye Edilen Tüketim Tarihi"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__date
msgid "Date"
msgstr "Tarih"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__gs1_decimal_usage
msgid "Decimal"
msgstr "Ondalık"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__location_dest
msgid "Destination location"
msgstr "Hedef konum"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__encoding
msgid "Encoding"
msgstr "Kodlama"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__expiration_date
msgid "Expiration Date"
msgstr "Son Kullanma Tarihi"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_nomenclature__gs1_separator_fnc1
msgid "FNC1 Separator"
msgstr "FNC1 Ayracı"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__gs1_content_type
msgid "GS1 Content Type"
msgstr "GS1 İçerik Türü"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__encoding__gs1-128
msgid "GS1-128"
msgstr "GS1-128"

#. module: barcodes_gs1_nomenclature
#: model:ir.model,name:barcodes_gs1_nomenclature.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Yönlendirme"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__gs1_decimal_usage
msgid ""
"If True, use the last digit of AI to determine where the first decimal is"
msgstr ""
"Doğru ise, ilk ondalık sayının nerede olduğunu belirlemek için AI'nın son "
"basamağını kullanın"

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "Invalid barcode: can't be formated as date"
msgstr "Geçersiz barkod: tarih olarak biçimlendirilemez"

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "Invalid barcode: the check digit is incorrect"
msgstr "Geçersiz barkod: kontrol basamağı yanlış"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_nomenclature__is_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__is_gs1_nomenclature
msgid "Is GS1 Nomenclature"
msgstr "GS1 Terminolojisi"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__location
msgid "Location"
msgstr "Konum"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "Lot"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__measure
msgid "Measure"
msgstr "Ölçüm"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__identifier
msgid "Numeric Identifier"
msgstr "Sayısal Tanımlayıcı"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__pack_date
msgid "Pack Date"
msgstr "Paket Tarihi"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__package
msgid "Package"
msgstr "Paket"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__package_type
msgid "Package Type"
msgstr "Paket Türü"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__quantity
msgid "Quantity"
msgstr "Miktar"

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_nomenclature.py:0
#, python-format
msgid "The FNC1 Separator Alternative is not a valid Regex: "
msgstr "FNC1 Ayırıcı Alternatifi geçerli bir Normal İfade değil:"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__gs1_content_type
msgid ""
"The GS1 content type defines what kind of data the rule will process the "
"barcode as:        * Date: the barcode will be converted into a Odoo "
"datetime;        * Measure: the barcode's value is related to a specific "
"UoM;        * Numeric Identifier: fixed length barcode following a specific "
"encoding;        * Alpha-Numeric Name: variable length barcode."
msgstr ""
"GS1 içerik türü, kuralın barkodu ne tür verileri işleyeceğini tanımlar:"
"       * Tarih: barkod bir Odoo tarih saatine dönüştürülür;       * Ölçü: "
"barkodun değeri belirli bir ölçü birimiyle ilgilidir;       * Sayısal "
"Tanımlayıcı: belirli bir kodlamayı izleyen sabit uzunluklu barkod;       * "
"Alfa-Sayısal Ad: değişken uzunluklu barkod."

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_rule.py:0
#, python-format
msgid "The rule pattern \"%s\" is not a valid Regex: "
msgstr "\"%s\" kural kalıbı geçerli bir Normal İfade değil:"

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_rule.py:0
#, python-format
msgid ""
"The rule pattern \"%s\" is not valid, it needs two groups:\n"
"\t- A first one for the Application Identifier (usually 2 to 4 digits);\n"
"\t- A second one to catch the value."
msgstr ""
"\"%s\" kural kalıbı geçerli değil, iki gruba ihtiyacı var:\n"
"\t- İlki Uygulama Tanımlayıcı için (genellikle 2 ila 4 basamak);\n"
"\t- İkincisi değeri yakalamak için."

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_nomenclature.py:0
#, python-format
msgid ""
"There is something wrong with the barcode rule \"%s\" pattern.\n"
"If this rule uses decimal, check it can't get sometime else than a digit as last char for the Application Identifier.\n"
"Check also the possible matched values can only be digits, otherwise the value can't be casted as a measure."
msgstr ""
"Barkod kuralı \"%s\" deseninde bir sorun var.\n"
"Bu kural ondalık sayı kullanıyorsa, Uygulama Tanımlayıcısı için son karakter olarak bir rakamdan başka bir zaman alamadığını kontrol edin.\n"
"Ayrıca olası eşleşen değerlerin yalnızca rakam olabileceğini kontrol edin, aksi takdirde değer bir ölçü olarak kullanılamaz."

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_nomenclature__is_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__is_gs1_nomenclature
msgid ""
"This Nomenclature use the GS1 specification, only GS1-128 encoding rules is "
"accepted is this kind of nomenclature."
msgstr ""
"Bu İsimlendirme, GS1 spesifikasyonunu kullanır, bu tür bir isimlendirmede "
"sadece GS1-128 kodlama kuralları kabul edilir."

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "This barcode can't be parsed by any barcode rules."
msgstr "Bu barkod, herhangi bir barkod kuralı tarafından ayrıştırılamaz."

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "This barcode can't be partially or fully parsed."
msgstr "Bu barkod kısmen veya tamamen ayrıştırılamaz."

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__encoding
msgid ""
"This rule will apply only if the barcode is encoded with the specified "
"encoding"
msgstr ""
"Bu kural okunan barkod yalnızca belirtilen kodlama ile barkod kodlanmış ise "
"uygulanacaktır."

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__type
msgid "Type"
msgstr "Tür"
