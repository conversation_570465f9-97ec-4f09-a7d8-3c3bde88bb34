<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="account_payment_term_advance" model="account.payment.term">
            <field name="name">90 days, on the 10th</field>
            <field name="note">Payment terms: 90 days, on the 10th</field>
            <field name="line_ids" eval="[
                Command.clear(),
                Command.create({'value': 'percent', 'value_amount': 100.0, 'delay_type': 'days_end_of_month_on_the','nb_days': 90, 'days_next_month': 10})]"/>
        </record>
    </data>
</odoo>
