<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="account_move_send_form" model="ir.ui.view">
        <field name="name">account.move.send.form</field>
        <field name="model">account.move.send</field>
        <field name="inherit_id" ref="account.account_move_send_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='warnings']" position="inside">
                <field name="show_ubl_company_warning" invisible="1"/>
                <div class="alert alert-info"
                     role="alert"
                     invisible="not checkbox_ubl_cii_xml or not ubl_partner_warning">
                    <field name="ubl_partner_warning"/>
                </div>
                <div class="alert alert-info"
                     role="alert"
                     invisible="not checkbox_ubl_cii_xml or not show_ubl_company_warning">
                    Please fill in Peppol EAS and Peppol Endpoint in your company form to generate a complete file.
                </div>
            </xpath>
            <xpath expr="//div[@name='advanced_options']" position="inside">
                <field name="enable_ubl_cii_xml" invisible="1"/>
                <div name="option_xml"
                     invisible="not enable_ubl_cii_xml">
                    <!-- Use one field as the label for another -->
                    <field name="checkbox_ubl_cii_xml" nolabel="1" style="display: inline-block"/>
                    <field name="checkbox_ubl_cii_label" style="position: absolute"/>
                </div>
            </xpath>
        </field>
    </record>

</odoo>
