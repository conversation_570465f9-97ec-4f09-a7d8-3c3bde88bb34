# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_ubl_cii
# 
# Translators:
# Wil Odoo, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:33+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid ""
"%s should have a KVK or OIN number: the Peppol e-address (EAS) should be "
"'0106' or '0190'."
msgstr ""
"%s sollte eine KVK- oder OIN-Nummer haben: die Peppol-E-Adresse (EAS) sollte"
" „0106“ oder „0190“ sein."

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0002
msgid ""
"0002 - System Information et Repertoire des Entreprise et des "
"Etablissements: SIRENE"
msgstr ""
"0002 - Systeminformation und Repertoire des Entreprise et des "
"Etablissements: SIRENE"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0007
msgid "0007 - Organisationsnummer (Swedish legal entities)"
msgstr "0007 - Organisationsnummer (Swedish legal entities)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0009
msgid "0009 - SIRET-CODE"
msgstr "0009 - SIRET-CODE"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0037
msgid "0037 - LY-tunnus"
msgstr "0037 - LY-tunnus"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0060
msgid "0060 - Data Universal Numbering System (D-U-N-S Number)"
msgstr "0060 - Data Universal Numbering System (D-U-N-S Number)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0088
msgid "0088 - EAN Location Code"
msgstr "0088 - EAN Location Code"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0096
msgid "0096 - DANISH CHAMBER OF COMMERCE Scheme (EDIRA compliant)"
msgstr "0096 - DANISH CHAMBER OF COMMERCE Scheme (EDIRA compliant)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0097
msgid "0097 - FTI - Ediforum Italia, (EDIRA compliant)"
msgstr "0097 - FTI - Ediforum Italien, (EDIRA-konform)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0106
msgid ""
"0106 - Association of Chambers of Commerce and Industry in the Netherlands, "
"(EDIRA compliant)"
msgstr ""
"0106 - Association of Chambers of Commerce and Industry in the Netherlands, "
"(EDIRA compliant)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0130
msgid "0130 - Directorates of the European Commission"
msgstr "0130 - Directorates of the European Commission"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0135
msgid "0135 - SIA Object Identifiers"
msgstr "0135 - SIA Object Identifiers"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0142
msgid "0142 - SECETI Object Identifiers"
msgstr "0142 - SECETI Object Identifiers"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0151
msgid "0151 - Australian Business Number (ABN) Scheme"
msgstr "0151 - Australian Business Number (ABN) Scheme"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0183
msgid "0183 - Swiss Unique Business Identification Number (UIDB)"
msgstr "0183 - Swiss Unique Business Identification Number (UIDB)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0184
msgid "0184 - DIGSTORG"
msgstr "0184 - DIGSTORG"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0188
msgid "0188 - Corporate Number of The Social Security and Tax Number System"
msgstr "0188 - Corporate Number of The Social Security and Tax Number System"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0190
msgid "0190 - Dutch Originator's Identification Number"
msgstr "0190 - Dutch Originator's Identification Number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0191
msgid ""
"0191 - Centre of Registers and Information Systems of the Ministry of "
"Justice"
msgstr ""
"0191 - Centre of Registers and Information Systems of the Ministry of "
"Justice"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0192
msgid "0192 - Enhetsregisteret ved Bronnoysundregisterne"
msgstr "0192 - Enhetsregisteret ved Bronnoysundregisterne"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0193
msgid "0193 - UBL.BE party identifier"
msgstr "0193 - UBL.BE party identifier"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0195
msgid "0195 - Singapore UEN identifier"
msgstr "0195 - Singapore UEN identifier"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0196
msgid "0196 - Kennitala - Iceland legal id for individuals and legal entities"
msgstr ""
"0196 - Kennitala - Iceland legal id for individuals and legal entities"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0198
msgid "0198 - ERSTORG"
msgstr "0198 - ERSTORG"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0199
msgid "0199 - Legal Entity Identifier (LEI)"
msgstr "0199 - Legal Entity Identifier (LEI)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0200
msgid "0200 - Legal entity code (Lithuania)"
msgstr "0200 - Legal entity code (Lithuania)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0201
msgid "0201 - Codice Univoco Unità Organizzativa iPA"
msgstr "0201 - Codice Univoco Unità Organizzativa iPA"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0202
msgid "0202 - Indirizzo di Posta Elettronica Certificata"
msgstr "0202 - Indirizzo di Posta Elettronica Certificata"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0204
msgid "0204 - Leitweg-ID"
msgstr "0204 - Leitweg-ID"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0208
msgid "0208 - Numero d'entreprise / ondernemingsnummer / Unternehmensnummer"
msgstr "0208 - Numero d'entreprise / ondernemingsnummer / Unternehmensnummer"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0209
msgid "0209 - GS1 identification keys"
msgstr "0209 - GS1 identification keys"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0210
msgid "0210 - CODICE FISCALE"
msgstr "0210 - CODICE FISCALE"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0211
msgid "0211 - PARTITA IVA"
msgstr "0211 - PARTITA IVA"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0212
msgid "0212 - Finnish Organization Identifier"
msgstr "0212 - Finnish Organization Identifier"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0213
msgid "0213 - Finnish Organization Value Add Tax Identifier"
msgstr "0213 - Finnish Organization Value Add Tax Identifier"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0215
msgid "0215 - Net service ID"
msgstr "0215 - Net service ID"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0216
msgid "0216 - OVTcode"
msgstr "0216 - OVTcode"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0221
msgid "0221 - The registered number of the qualified invoice issuer (Japan)"
msgstr "0221 - The registered number of the qualified invoice issuer (Japan)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0230
msgid "0230 - National e-Invoicing Framework (Malaysia)"
msgstr "0230 - National e-Invoicing Framework (Malaysia)"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "1.0"
msgstr "1.0"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_facturx_export_22
msgid "42"
msgstr "42"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9901
msgid "9901 - Danish Ministry of the Interior and Health"
msgstr "9901 - Dänisches Innen- und Gesundheitsministerium"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9910
msgid "9910 - Hungary VAT number"
msgstr "9910 - Hungary VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9913
msgid "9913 - Business Registers Network"
msgstr "9913 - Netzwerk der Unternehmensregister"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9914
msgid "9914 - Österreichische Umsatzsteuer-Identifikationsnummer"
msgstr "9914 - Österreichische Umsatzsteuer-Identifikationsnummer"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9915
msgid "9915 - Österreichisches Verwaltungs bzw. Organisationskennzeichen"
msgstr "9915 - Österreichisches Verwaltungs- bzw. Organisationskennzeichen"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9918
msgid ""
"9918 - SOCIETY FOR WORLDWIDE INTERBANK FINANCIAL, TELECOMMUNICATION "
"S.W.I.F.T"
msgstr ""
"9918 - SOCIETY FOR WORLDWIDE INTERBANK FINANCIAL, TELECOMMUNICATION "
"S.W.I.F.T"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9919
msgid "9919 - Kennziffer des Unternehmensregisters"
msgstr "9919 - Kennziffer des Unternehmensregisters"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9920
msgid "9920 - Agencia Española de Administración Tributaria"
msgstr "9920 - Agencia Española de Administración Tributaria"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9922
msgid "9922 - Andorra VAT number"
msgstr "9922 - Andorra VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9923
msgid "9923 - Albania VAT number"
msgstr "9923 - Albania VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9924
msgid "9924 - Bosnia and Herzegovina VAT number"
msgstr "9924 - Bosnia and Herzegovina VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9925
msgid "9925 - Belgium VAT number"
msgstr "9925 - Belgium VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9926
msgid "9926 - Bulgaria VAT number"
msgstr "9926 - Bulgaria VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9927
msgid "9927 - Switzerland VAT number"
msgstr "9927 - Switzerland VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9928
msgid "9928 - Cyprus VAT number"
msgstr "9928 - Cyprus VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9929
msgid "9929 - Czech Republic VAT number"
msgstr "9929 - Czech Republic VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9930
msgid "9930 - Germany VAT number"
msgstr "9930 - Germany VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9931
msgid "9931 - Estonia VAT number"
msgstr "9931 - Estonia VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9932
msgid "9932 - United Kingdom VAT number"
msgstr "9932 - United Kingdom VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9933
msgid "9933 - Greece VAT number"
msgstr "9933 - Greece VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9934
msgid "9934 - Croatia VAT number"
msgstr "9934 - Croatia VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9935
msgid "9935 - Ireland VAT number"
msgstr "9935 - Ireland VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9936
msgid "9936 - Liechtenstein VAT number"
msgstr "9936 - Liechtenstein VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9937
msgid "9937 - Lithuania VAT number"
msgstr "9937 - Lithuania VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9938
msgid "9938 - Luxemburg VAT number"
msgstr "9938 - Luxemburg VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9939
msgid "9939 - Latvia VAT number"
msgstr "9939 - Lettland MwSt.-Nummer"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9940
msgid "9940 - Monaco VAT number"
msgstr "9940 - Monaco VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9941
msgid "9941 - Montenegro VAT number"
msgstr "9941 - Montenegro VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9942
msgid "9942 - Macedonia, the former Yugoslav Republic of VAT number"
msgstr "9942 - Macedonia, the former Yugoslav Republic of VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9943
msgid "9943 - Malta VAT number"
msgstr "9943 - Malta VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9944
msgid "9944 - Netherlands VAT number"
msgstr "9944 - Netherlands VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9945
msgid "9945 - Poland VAT number"
msgstr "9945 - Poland VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9946
msgid "9946 - Portugal VAT number"
msgstr "9946 - Portugal VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9947
msgid "9947 - Romania VAT number"
msgstr "9947 - Romania VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9948
msgid "9948 - Serbia VAT number"
msgstr "9948 - Serbia VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9949
msgid "9949 - Slovenia VAT number"
msgstr "9949 - Slovenia VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9950
msgid "9950 - Slovakia VAT number"
msgstr "9950 - Slovakia VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9951
msgid "9951 - San Marino VAT number"
msgstr "9951 - San Marino VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9952
msgid "9952 - Türkiye VAT number"
msgstr "9952 - Steuernummer für Türkei"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9953
msgid "9953 - Holy See (Vatican City State) VAT number"
msgstr "9953 - Holy See (Vatican City State) VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9955
msgid "9955 - Swedish VAT number"
msgstr "9955 - Swedish VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9957
msgid "9957 - French VAT number"
msgstr "9957 - French VAT number"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9959
msgid "9959 - Employer Identification Number (EIN, USA)"
msgstr "9959 - Employer Identification Number (EIN, USA)"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "A payment of %s was detected."
msgstr "Eine Zahlung von %s wurde entdeckt."

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_a_nz
msgid "A-NZ BIS Billing 3.0"
msgstr "A-NZ BIS Billing 3.0"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_move_send
msgid "Account Move Send"
msgstr "Kontobuchung senden"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Articles 226 items 11 to 15 Directive 2006/112/EN"
msgstr "Artikel 226 Nummern 11 bis 15 Richtlinie 2006/112/EN"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "At least one of the following fields %s is required on %s."
msgstr "Mindestens eines der folgenden Felder %s wird benötigt für %s."

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_bank_statement_line__ubl_cii_xml_id
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move__ubl_cii_xml_id
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_payment__ubl_cii_xml_id
msgid "Attachment"
msgstr "Dateianhang"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__ubl_cii_format__ubl_bis3
msgid "BIS Billing 3.0"
msgstr "BIS Billing 3.0"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__ubl_cii_format__ubl_a_nz
msgid "BIS Billing 3.0 A-NZ"
msgstr "BIS Billing 3.0 A-NZ"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__ubl_cii_format__ubl_sg
msgid "BIS Billing 3.0 SG"
msgstr "BIS Billing 3.0 SG"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_de
msgid "BIS3 DE (XRechnung)"
msgstr "BIS3 DE (XRechnung)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move_send__checkbox_ubl_cii_label
msgid "Checkbox Ubl Cii Label"
msgstr "Kontrollkästchen Ubl Cii Xml Bezeichnung"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move_send__checkbox_ubl_cii_xml
msgid "Checkbox Ubl Cii Xml"
msgstr "Kontrollkästchen Ubl Cii Xml"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_partner__peppol_eas
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_users__peppol_eas
msgid ""
"Code used to identify the Endpoint for BIS Billing 3.0 and its derivatives.\n"
"             List available at https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"
msgstr ""
"Code zur Identifizierung des Endpoint für BIS Billing 3.0 und seine Ableitungen.\n"
"              Liste verfügbar unter https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_common
msgid ""
"Common functions for EDI documents: generate the data, the constraints, etc"
msgstr ""
"Gemeinsame Funktionen für EDI-Dokumente: Generierung der Daten, der "
"Einschränkungen usw."

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move_send__show_ubl_company_warning
msgid "Company warning"
msgstr "Warnung an Unternehmen"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#, python-format
msgid "Conditional cash/payment discount"
msgstr "Bedingtes Skonto/Bedingter Zahlungsrabatt"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen "

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#, python-format
msgid ""
"Could not retrieve currency: %s. Did you enable the multicurrency option and"
" activate the currency?"
msgstr ""
"Die Währung konnte nicht abgerufen werden: %s. Haben Sie die Option für "
"mehrere Währungen eingeschaltet und die Währung aktiviert?"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Could not retrieve the tax: %s %% for line '%s'."
msgstr "Die Steuer konnte nicht abgerufen werden: %s %% für Zeile „%s“."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Could not retrieve the unit of measure for line with label '%s'."
msgstr ""
"Die Maßeinheit für die Zeile mit Bezeichnung „%s“ konnte nicht abgerufen "
"werden."

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_efff
msgid "E-FFF (BE)"
msgstr "E-FFF (BE)"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.res_partner_view_search
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.res_partner_view_tree
msgid "EDI Format"
msgstr "EDI-Format"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "EN 16931"
msgstr "EN 16931"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "Each invoice line shall have one and only one tax."
msgstr "Jede Rechnungszeile darf nur eine einzige Steuer enthalten."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "Each invoice line should have a product or a label."
msgstr ""
"Jede Rechnungszeile sollte mindestens ein Produkt und eine Bezeichnung "
"enthalten."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Each invoice line should have at least one tax."
msgstr "Jede Rechnungszeile muss mindestens eine Steuer enthalten."

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.view_partner_property_form
msgid "Electronic Invoicing"
msgstr "Elektronische Rechnungsstellung"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move_send__enable_ubl_cii_xml
msgid "Enable Ubl Cii Xml"
msgstr "Ubl Cii Xml aktivieren"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/wizard/account_move_send.py:0
#, python-format
msgid "Errors occurred while creating the EDI document (format: %s):"
msgstr ""
"Bei der Erstellung des EDI-Dokuments sind Fehler aufgetreten (Format: %s):"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Export outside the EU"
msgstr "Export außerhalb der EU"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__ubl_cii_format__facturx
msgid "Factur-X (CII)"
msgstr "Factur-X (CII)"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_cii
msgid "Factur-x/XRechnung CII 2.2.0"
msgstr "Factur-x/XRechnung CII 2.2.0"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid ""
"For intracommunity supply, the actual delivery date or the invoicing period "
"should be included."
msgstr ""
"Bei innergemeinschaftlichen Lieferungen sollte das tatsächliche Lieferdatum "
"oder der Rechnungszeitraum angegeben werden."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "For intracommunity supply, the delivery address should be included."
msgstr ""
"Bei innergemeinschaftlichen Lieferungen sollte die Lieferadresse angegeben "
"werden."

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__ubl_cii_format
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__ubl_cii_format
msgid "Format"
msgstr "Format"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Format used to import the invoice: %s"
msgstr "Für den Import der Rechnung verwendetes Format: %s"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_company__invoice_is_ubl_cii
msgid "Generate Peppol format by default"
msgstr "Peppol-Format standardmäßig generieren"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__hide_peppol_fields
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__hide_peppol_fields
msgid "Hide Peppol Fields"
msgstr "Peppol-Felder ausblenden"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Intra-Community supply"
msgstr "Innergemeinschaftliche Lieferung"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "Invoice generated by Odoo"
msgstr "Rechnung generiert von Odoo"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_move
msgid "Journal Entry"
msgstr "Journalbuchung"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__ubl_cii_format__nlcius
msgid "NLCIUS"
msgstr "NLCIUS"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid ""
"No gross price, net price nor line subtotal amount found for line in xml"
msgstr ""
"Weder Bruttopreis oder Nettopreis noch Zeile für Zwischensumme für Zeile in "
"xml gefunden"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "Odoo"
msgstr "Odoo"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move_send__ubl_partner_warning
msgid "Partner warning"
msgstr "Warnung an Partner"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
#, python-format
msgid ""
"Peppol EAS code 9901 is deprecated. Please use a different Danish EAS code "
"instead."
msgstr ""
"Der Peppol-EAS-Code 9901 ist veraltet. Bitte verwenden Sie stattdessen einen"
" anderen dänischen EAS-Code."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
#, python-format
msgid "Peppol EAS code 9955 is deprecated. Please use 0007 instead."
msgstr ""
"Der Peppol-EAS-Code 9955 ist veraltet. Bitte verwenden Sie stattdessen 0007."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
#, python-format
msgid ""
"Peppol EAS codes 0037, 0212, 0213, 0215 are deprecated. Please use 0216 "
"instead."
msgstr ""
"Die Peppol-EAS-Codes 0037, 0212, 0213, 0215 sind veraltet. Bitte verwenden "
"Sie stattdessen 0216."

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__peppol_endpoint
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__peppol_endpoint
msgid "Peppol Endpoint"
msgstr "Peppol Endpoint"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__peppol_eas
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__peppol_eas
msgid "Peppol e-address (EAS)"
msgstr "Peppol E-Adresse (EAS)"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_config_settings__invoice_is_ubl_cii
msgid "Peppol format"
msgstr "Peppol-Format"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_move_send_form
msgid ""
"Please fill in Peppol EAS and Peppol Endpoint in your company form to "
"generate a complete file."
msgstr ""
"Bitte füllen Sie Peppol-EAS und Peppol-Endpunkt in Ihrem "
"Unternehmensformular aus, um eine vollständige Datei zu generieren."

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_ir_actions_report
msgid "Report Action"
msgstr "Berichtsaktion"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_sg
msgid "SG BIS Billing 3.0"
msgstr "SG BIS Billing 3.0"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_nl
msgid "SI-UBL 2.0 (NLCIUS)"
msgstr "SI-UBL 2.0 (NLCIUS)"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Tax '%s' is invalid: %s"
msgstr "Steuer „%s“ ist ungültig: %s"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
#, python-format
msgid ""
"The Peppol endpoint is not valid. It should contain exactly 10 digits "
"(Company Registry number).The expected format is: **********"
msgstr ""
"Der Peppol-Endpunkt ist nicht gültig. Er sollte genau 10 Ziffern enthalten "
"(Unternehmensregisternummer), das erwartete Format ist: **********"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
#, python-format
msgid "The Peppol endpoint is not valid. The expected format is: **********"
msgstr ""
"Der Peppol-Endpunkt ist nicht gültig. Das erwartete Format ist: **********"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
#, python-format
msgid ""
"The Peppol endpoint is not valid. The expected format is: **************"
msgstr ""
"Der Peppol-Endpunkt ist nicht gültig. Das erwartete Format ist: "
"**************"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid ""
"The VAT number of the supplier does not seem to be valid. It should be of "
"the form: NO179728982MVA."
msgstr ""
"Die MwSt.-Nummer des Lieferanten scheint nicht gültig zu sein. Sie sollte "
"dieses Format haben: NO179728982MVA."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "The VAT of the %s should be prefixed with its country code."
msgstr "Die USt-IdNr. von %s sollte den Präfix seines Ländercodes enthalten."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "The country is required for the %s."
msgstr "Das Land ist für %s erforderlich."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#, python-format
msgid "The currency '%s' is not active."
msgstr "Die Währung „%s“ ist nicht aktiv."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "The element %s is required on %s."
msgstr "Das Element %s ist erforderlich für %s."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "The field %s is required on %s."
msgstr "Das Feld %s ist erforderlich für %s."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#, python-format
msgid ""
"The field 'Sanitized Account Number' is required on the Recipient Bank."
msgstr ""
"Das Feld „Bereinigte Kontonummer“ ist für die Empfängerbank erforderlich."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/wizard/account_move_send.py:0
#, python-format
msgid ""
"The following partners are missing Peppol EAS or Peppol Endpoint field: %s. "
"Please check those in their Accounting tab. Otherwise, the generated files "
"will be incomplete."
msgstr ""
"Die folgenden Partner fehlen im Feld Peppol EAS oder Peppol-Endpunkt: %s. "
"Bitte sehen Sie im Buchhaltungsreiter nach. Andernfalls werden die "
"generierten Dateien unvollständig sein."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#, python-format
msgid ""
"The invoice has been converted into a credit note and the quantities have "
"been reverted."
msgstr ""
"Die Rechnung wurde in eine Gutschrift umgewandelt und die Mengen rückgängig "
"gemacht."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/wizard/account_move_send.py:0
#, python-format
msgid ""
"This partner is missing Peppol EAS or Peppol Endpoint field. Please check "
"those in its Accounting tab or the generated file will be incomplete."
msgstr ""
"Dieser Partner fehlt im Feld Peppol EAS oder Peppol-Endpunkt. Bitte sehen "
"Sie im Buchhaltungsreiter nach. Andernfalls werden die generierten Dateien "
"unvollständig sein."

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_20
msgid "UBL 2.0"
msgstr "UBL 2.0"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_21
msgid "UBL 2.1"
msgstr "UBL 2.1"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_bis3
msgid "UBL BIS Billing 3.0.12"
msgstr "UBL BIS Billing 3.0.12"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_bank_statement_line__ubl_cii_xml_file
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move__ubl_cii_xml_file
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_payment__ubl_cii_xml_file
msgid "UBL/CII File"
msgstr "UBL/CII-Datei"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_partner__peppol_endpoint
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_users__peppol_endpoint
msgid ""
"Unique identifier used by the BIS Billing 3.0 and its derivatives, also "
"known as 'Endpoint ID'."
msgstr ""
"Eindeutige Kennung, die von BIS Billing 3.0 und Ableitungen verwendet wird, "
"auch bekannt als „Endpunkt-ID“."

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#, python-format
msgid ""
"When the Canary Island General Indirect Tax (IGIC) applies, the tax rate on "
"each invoice line should be greater than 0."
msgstr ""
"Wenn die Allgemeine Indirekte Steuer der Kanarischen Inseln (IGIC) gilt, "
"muss der Steuersatz in jeder Rechnungszeile größer als 0 sein."

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__ubl_cii_format__xrechnung
msgid "XRechnung CIUS"
msgstr "XRechnung CIUS"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#, python-format
msgid ""
"You should include at least one tax per invoice line. [BR-CO-04]-Each "
"Invoice line (BG-25) shall be categorized with an Invoiced item VAT category"
" code (BT-151)."
msgstr ""
"Sie sollten mindestens eine Steuer pro Rechnungszeile angeben. [BR-"
"CO-04]-Jede Rechnungszeile (BG-25) muss mit einem Mehrwertsteuer-"
"Kategoriecode für die Rechnungszeile (BT-151) kategorisiert werden."

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "factur-x.xml"
msgstr "factur-x.xml"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "fx"
msgstr "fx"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
msgstr "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
