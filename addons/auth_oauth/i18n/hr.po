# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_oauth
# 
# Translators:
# <PERSON><PERSON><PERSON> <karol<PERSON>.ton<PERSON>@storm.hr>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.providers
msgid "- or -"
msgstr "— ili —"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Tutorial"
msgstr ""

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
#, python-format
msgid "Access Denied"
msgstr "Pristup odbijen / zabranjen"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__auth_oauth_google_enabled
msgid "Allow users to sign in with Google"
msgstr "Dozvoli prijavu sa Google računom"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Allow users to sign in with their Google account"
msgstr "Dozvoli prijavu sa Google računom"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__enabled
msgid "Allowed"
msgstr "Dopušteno"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__auth_endpoint
msgid "Authorization URL"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__css_class
msgid "CSS class"
msgstr "CSS klasa"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__client_id
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__auth_oauth_google_client_id
msgid "Client ID"
msgstr "ID klijenta"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Client ID:"
msgstr "ID klijenta:"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_config_settings
msgid "Config Settings"
msgstr "Konfiguracija postavki"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__data_endpoint
msgid "Data Endpoint"
msgstr ""

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Google Authentication"
msgstr "Google Authentikacija"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__id
msgid "ID"
msgstr "ID"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__write_date
msgid "Last Updated on"
msgstr "Vrijeme promjene"

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_auth_oauth_provider__body
msgid "Link text in Login Dialog"
msgstr "tekst linka u dijalogu prijave"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_facebook
msgid "Log in with Facebook"
msgstr "Prijava preko Facebooka"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_google
msgid "Log in with Google"
msgstr "Prijava preko Google"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_openerp
msgid "Log in with Odoo.com"
msgstr "Prijava preko Odoo.com"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__body
msgid "Login button label"
msgstr "Natpis na gumbu za prijavu"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_access_token
msgid "OAuth Access Token"
msgstr "Token OAuth pristupa"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_provider_id
msgid "OAuth Provider"
msgstr "Pružatelj OAuth"

#. module: auth_oauth
#: model:ir.ui.menu,name:auth_oauth.menu_oauth_providers
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "OAuth Providers"
msgstr "Pružatelji OAuth"

#. module: auth_oauth
#: model:ir.model.constraint,message:auth_oauth.constraint_res_users_uniq_users_oauth_provider_oauth_uid
msgid "OAuth UID must be unique per provider"
msgstr "OAuth UID mora biti jedinstven po pružatelju"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_uid
msgid "OAuth User ID"
msgstr "OAuth Korisnički ID"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_auth_oauth_provider
msgid "OAuth2 provider"
msgstr "OAuth2 pružatelj"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_users_form
msgid "Oauth"
msgstr "Oauth"

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users__oauth_uid
msgid "Oauth Provider user_id"
msgstr "user_id OAuth pružatelja usluge"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__name
msgid "Provider name"
msgstr "Naziv pružatelja usluge"

#. module: auth_oauth
#: model:ir.actions.act_window,name:auth_oauth.action_oauth_provider
msgid "Providers"
msgstr "Pružatelji"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__scope
msgid "Scope"
msgstr "Opseg"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__server_uri_google
msgid "Server uri"
msgstr "ULI Poslužitelja"

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
#, python-format
msgid "Sign up is not allowed on this database."
msgstr "Prijava nije dozvoljena na ovoj bazi."

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_ir_config_parameter
msgid "System Parameter"
msgstr "Sistemski parametar"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_users
msgid "User"
msgstr "Korisnik"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__validation_endpoint
msgid "UserInfo URL"
msgstr ""

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
#, python-format
msgid ""
"You do not have access to this database or your invitation has expired. "
"Please ask for an invitation and be sure to follow the link in your "
"invitation email."
msgstr ""
"Nemate pristup ovoj bazi ili je Vaša pozivnica istekla. Molimo zatražite "
"pozivnicu i kliknite na link u e-mailu prije nego istekne."

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_form
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_tree
msgid "arch"
msgstr "arhitektura"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "e.g. 1234-xyz.apps.googleusercontent.com"
msgstr "npr. 1234-xyz.apps.googleusercontent.com"
