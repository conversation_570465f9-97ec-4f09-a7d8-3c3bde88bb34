# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_oauth
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.providers
msgid "- or -"
msgstr "- hoặc -"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Tutorial"
msgstr "<i class=\"oi oi-fw oi-arrow-right\"/>Hướng dẫn"

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
#, python-format
msgid "Access Denied"
msgstr "Truy cập bị từ chối"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__auth_oauth_google_enabled
msgid "Allow users to sign in with Google"
msgstr "Cho phép người dùng đăng nhập bằng Google"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Allow users to sign in with their Google account"
msgstr "Cho phép người dùng đăng nhập bằng tài khoản Google của họ"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__enabled
msgid "Allowed"
msgstr "Được phép"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__auth_endpoint
msgid "Authorization URL"
msgstr "URL Ủy quyền"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__css_class
msgid "CSS class"
msgstr "Bộ chọn class trong CSS"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__client_id
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__auth_oauth_google_client_id
msgid "Client ID"
msgstr "ID khách hàng"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Client ID:"
msgstr "ID khách hàng:"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_config_settings
msgid "Config Settings"
msgstr "Cài đặt cấu hình"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__data_endpoint
msgid "Data Endpoint"
msgstr "Endpoint dữ liệu"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "Google Authentication"
msgstr "Xác thực Google "

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__id
msgid "ID"
msgstr "ID"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_auth_oauth_provider__body
msgid "Link text in Login Dialog"
msgstr "Văn bản liên kết trong Hộp thoại đăng nhập"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_facebook
msgid "Log in with Facebook"
msgstr "Đăng nhập với Facebook"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_google
msgid "Log in with Google"
msgstr "Đăng nhập với Google"

#. module: auth_oauth
#: model:auth.oauth.provider,body:auth_oauth.provider_openerp
msgid "Log in with Odoo.com"
msgstr "Đăng nhập với Odoo.com"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__body
msgid "Login button label"
msgstr "Nhãn nút đăng nhập"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_access_token
msgid "OAuth Access Token"
msgstr "Token truy cập OAuth"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_provider_id
msgid "OAuth Provider"
msgstr "Nhà cung cấp OAuth "

#. module: auth_oauth
#: model:ir.ui.menu,name:auth_oauth.menu_oauth_providers
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "OAuth Providers"
msgstr "Nhà cung cấp OAuth "

#. module: auth_oauth
#: model:ir.model.constraint,message:auth_oauth.constraint_res_users_uniq_users_oauth_provider_oauth_uid
msgid "OAuth UID must be unique per provider"
msgstr "Mỗi nhà cung cấp có một UID OAuth riêng."

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_users__oauth_uid
msgid "OAuth User ID"
msgstr "ID người dùng OAuth "

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_auth_oauth_provider
msgid "OAuth2 provider"
msgstr "Nhà cung cấp OAuth2"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_users_form
msgid "Oauth"
msgstr "Oauth"

#. module: auth_oauth
#: model:ir.model.fields,help:auth_oauth.field_res_users__oauth_uid
msgid "Oauth Provider user_id"
msgstr "user_id của nhà cung cấp Oauth"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__name
msgid "Provider name"
msgstr "Tên nhà cung cấp"

#. module: auth_oauth
#: model:ir.actions.act_window,name:auth_oauth.action_oauth_provider
msgid "Providers"
msgstr "Nhà cung cấp "

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__scope
msgid "Scope"
msgstr "Phạm vi"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_res_config_settings__server_uri_google
msgid "Server uri"
msgstr "URL máy chủ"

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
#, python-format
msgid "Sign up is not allowed on this database."
msgstr "Không được phép đăng ký trên cơ sở dữ liệu này."

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_ir_config_parameter
msgid "System Parameter"
msgstr "Tham số hệ thống"

#. module: auth_oauth
#: model:ir.model,name:auth_oauth.model_res_users
msgid "User"
msgstr "Người dùng"

#. module: auth_oauth
#: model:ir.model.fields,field_description:auth_oauth.field_auth_oauth_provider__validation_endpoint
msgid "UserInfo URL"
msgstr "URL thông tin người dùng"

#. module: auth_oauth
#. odoo-python
#: code:addons/auth_oauth/controllers/main.py:0
#, python-format
msgid ""
"You do not have access to this database or your invitation has expired. "
"Please ask for an invitation and be sure to follow the link in your "
"invitation email."
msgstr ""
"Bạn không có quyền truy cập vào cơ sở dữ liệu này hoặc lời mời của bạn đã "
"hết hạn. Vui lòng yêu cầu một lời mời mới và nhấp vào liên kết trong email "
"mời của bạn."

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_form
#: model_terms:ir.ui.view,arch_db:auth_oauth.view_oauth_provider_tree
msgid "arch"
msgstr "arch"

#. module: auth_oauth
#: model_terms:ir.ui.view,arch_db:auth_oauth.res_config_settings_view_form
msgid "e.g. 1234-xyz.apps.googleusercontent.com"
msgstr "VD: 1234-xyz.apps.googleusercontent.com"
