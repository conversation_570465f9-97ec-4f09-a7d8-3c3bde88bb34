# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_proxy_client
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr "EDIアカウントプロキシユーザー"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_res_company__account_edi_proxy_client_ids
msgid "Account Edi Proxy Client"
msgstr "Ediアカウントプロキシ顧客"

#. module: account_edi_proxy_client
#: model_terms:ir.ui.view,arch_db:account_edi_proxy_client.view_form_account_edi_proxy_client_user
msgid "Account Journal"
msgstr "会計仕訳帳"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__active
msgid "Active"
msgstr "有効化"

#. module: account_edi_proxy_client
#: model:ir.model,name:account_edi_proxy_client.model_res_company
msgid "Companies"
msgstr "会社"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__company_id
msgid "Company"
msgstr "会社"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__create_uid
msgid "Created by"
msgstr "作成者"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__create_date
msgid "Created on"
msgstr "作成日"

#. module: account_edi_proxy_client
#: model:ir.model.fields.selection,name:account_edi_proxy_client.selection__account_edi_proxy_client_user__edi_mode__demo
msgid "Demo mode"
msgstr "デモモード"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__display_name
msgid "Display Name"
msgstr "表示名"

#. module: account_edi_proxy_client
#: model:ir.actions.act_window,name:account_edi_proxy_client.action_tree_account_edi_proxy_client_user
msgid "EDI Proxy User"
msgstr "EDIプロキシユーザー"

#. module: account_edi_proxy_client
#: model:ir.ui.menu,name:account_edi_proxy_client.menu_account_proxy_client_user
msgid "EDI Proxy Users"
msgstr "EDIプロキシユーザー"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_mode
msgid "EDI operating mode"
msgstr "EDI操作モード"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_identification
msgid "Edi Identification"
msgstr "Edi識別確認"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__id
msgid "ID"
msgstr "ID"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__id_client
msgid "Id Client"
msgstr "Idクライアント"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key
msgid "Private Key"
msgstr "プライベートキー"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key_filename
msgid "Private Key Filename"
msgstr "プライベートキーファイル名"

#. module: account_edi_proxy_client
#: model:ir.model.fields.selection,name:account_edi_proxy_client.selection__account_edi_proxy_client_user__edi_mode__prod
msgid "Production mode"
msgstr "本番モード"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__proxy_type
msgid "Proxy Type"
msgstr "プロキシタイプ"

#. module: account_edi_proxy_client
#: model:ir.model.fields,field_description:account_edi_proxy_client.field_account_edi_proxy_client_user__refresh_token
msgid "Refresh Token"
msgstr "リフレッシュトークン"

#. module: account_edi_proxy_client
#: model:ir.model.fields.selection,name:account_edi_proxy_client.selection__account_edi_proxy_client_user__edi_mode__test
msgid "Test mode"
msgstr "テストモード"

#. module: account_edi_proxy_client
#: model:ir.model.fields,help:account_edi_proxy_client.field_account_edi_proxy_client_user__private_key
msgid "The key to encrypt all the user's data"
msgstr "全ユーザーのデータを暗号化するキー"

#. module: account_edi_proxy_client
#: model:ir.model.fields,help:account_edi_proxy_client.field_account_edi_proxy_client_user__edi_identification
msgid "The unique id that identifies this user, typically the vat"
msgstr "このユーザーを識別する一意のID、通常vat"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. The url it tried to "
"contact was %s"
msgstr "このサービスが要求したURLからエラーが返されました。このサービスがコンタクトしようとしたURLは%s"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
#, python-format
msgid ""
"The url that this service requested returned an error. The url it tried to "
"contact was %s. %s"
msgstr "このサービスが要求したURLからエラーを返されました。このサービスがコンタクトしようとしたURLは%s.%s"

#. module: account_edi_proxy_client
#. odoo-python
#: code:addons/account_edi_proxy_client/models/account_edi_proxy_user.py:0
#, python-format
msgid ""
"The url that this service tried to contact does not exist. The url was %r"
msgstr "このサービスがコンタクトを試みたURL は存在しません。URL は %r"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_active_company_proxy
msgid "This company has an active user already created for this EDI type"
msgstr "この会社には、このEDIタイプ用に作成されたアクティブなユーザがいます。"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_active_edi_identification
msgid "This edi identification is already assigned to an active user"
msgstr "このEDI識別子は、すでにアクティブなユーザに割り当てられています。"

#. module: account_edi_proxy_client
#: model:ir.model.constraint,message:account_edi_proxy_client.constraint_account_edi_proxy_client_user_unique_id_client
msgid "This id_client is already used on another user."
msgstr "このid_clientはすでに別のユーザで使用されています"
