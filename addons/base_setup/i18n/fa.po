# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_setup
# 
# Translators:
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 12:31+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "<span class=\"fa fa-lg fa-users\" aria-label=\"Number of active users\"/>"
msgstr "<span class=\"fa fa-lg fa-users\" aria-label=\"Number of active users\"/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" invisible=\"active_user_count &gt; 1\">\n"
"                                        Active User\n"
"                                    </span>\n"
"                                    <span class=\"o_form_label\" invisible=\"active_user_count &lt;= 1\">\n"
"                                        Active Users\n"
"                                    </span>"
msgstr ""
"<span class=\"o_form_label\" invisible=\"active_user_count &gt; 1\">\n"
"کاربر فعال\n"
"</span>\n"
"<span class=\"o_form_label\" invisible=\"active_user_count &lt;= 1\">\n"
"کاربران فعال\n"
"</span>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" invisible=\"company_count &gt; 1\">\n"
"                                        Company\n"
"                                    </span>\n"
"                                    <span class=\"o_form_label\" invisible=\"company_count &lt;= 1\">\n"
"                                        Companies\n"
"                                    </span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"o_form_label\" invisible=\"company_count &gt; 1\">\n"
"شرکت\n"
"</span>\n"
"<span class=\"o_form_label\" invisible=\"company_count &lt;= 1\">\n"
"شرکت‌ها\n"
"</span>\n"
"<br/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" invisible=\"language_count &gt; 1\">\n"
"                                            Language\n"
"                                        </span>\n"
"                                        <span class=\"o_form_label\" invisible=\"language_count &lt;= 1\">\n"
"                                            Languages\n"
"                                        </span>"
msgstr ""
"<span class=\"o_form_label\" invisible=\"language_count &gt; 1\">\n"
"زبان\n"
"</span>\n"
"<span class=\"o_form_label\" invisible=\"language_count &lt;= 1\">\n"
"زبان‌‌ها\n"
"</span>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to choose your Geo "
"Provider."
msgstr ""
"<strong>ذخیره</strong>این صفحه و به اینجا بازگردید تا ارائه‌دهنده جغرافیایی "
"خود را انتخاب کنید."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up Cloudflare "
"turnstile."
msgstr ""
"<strong>ذخیره</strong> این صغحه و به اینجا بازگردید تا تنظیمات Cloudflare "
"Turnstile را انجام دهید."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up reCaptcha."
msgstr ""
"<strong>ذخیره</strong> این صفحه و به اینجا بازگردید تا reCaptcha را تنظیم "
"کنید."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr ""
"<strong>ذخیره</strong> این صفحه و به اینجا بازگردید تا ویژگی را تنظیم کنید."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "API Keys"
msgstr "کلیدهای API"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"API Keys allow your users to access Odoo with external tools when multi-"
"factor authentication is enabled."
msgstr ""
"کلیدهای API به کاربران شما این امکان را می‌دهند که با ابزارهای خارجی به Odoo"
" دسترسی پیدا کنند، زمانی که احراز هویت چندمرحله‌ای فعال باشد."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "About"
msgstr "درباره"

#. module: base_setup
#. odoo-python
#: code:addons/base_setup/controllers/main.py:0
#, python-format
msgid "Access Denied"
msgstr "دسترسی ممنوع است."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Add Languages"
msgstr "افزودن زبان‌ها"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Add fun feedback and motivate your employees"
msgstr "بازخوردهای سرگرم‌کننده اضافه کنید و کارکنان خود را تشویق کنید."

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_mail_plugin
msgid "Allow integration with the mail plugins"
msgstr "اجازه ادغام با افزونه‌های ایمیل را بدهید"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_calendar
msgid "Allow the users to synchronize their calendar  with Google Calendar"
msgstr ""
"به کاربران این امکان را بدهید که تقویم خود را با تقویم گوگل همگام‌سازی کنند."

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_microsoft_calendar
msgid "Allow the users to synchronize their calendar with Outlook Calendar"
msgstr ""
"به کاربران این امکان را بدهید که تقویم خود را با تقویم Outlook همگام‌سازی "
"کنند."

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_import
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Allow users to import data from CSV/XLS/XLSX/ODS files"
msgstr "اجازه دهید کاربران داده‌ها را از فایل‌های CSV/XLS/XLSX/ODS وارد کنند."

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__group_multi_currency
msgid "Allows to work in a multi currency environment"
msgstr "اجازه می‌دهد در یک محیط چند ارزی کار کنید."

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_voip
msgid "Asterisk (VoIP)"
msgstr "ستاره (VoIP)"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Automatically enrich your contact base with company data"
msgstr "به‌طور خودکار پایگاه تماس‌های خود را با داده‌های شرکتی غنی‌سازی کنید."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Automatically generate counterpart documents for orders/invoices between "
"companies"
msgstr ""
"به‌طور خودکار اسناد معادل برای سفارش‌ها/فاکتورها بین شرکت‌ها تولید کنید."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"By default, new users get highest access rights for all installed apps."
msgstr ""
"به‌طور پیش‌فرض، کاربران جدید بالاترین دسترسی را برای تمام برنامه‌های نصب‌شده"
" دریافت می‌کنند."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Choose the layout of your documents"
msgstr "طرح اسناد خود را انتخاب کنید"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_website_cf_turnstile
msgid "Cloudflare Turnstile"
msgstr "Cloudflare Turnstile"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Companies"
msgstr "شرکت‌ها"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_id
msgid "Company"
msgstr "شرکت"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_country_code
msgid "Company Country Code"
msgstr "کد کشور شرکت"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_informations
msgid "Company Informations"
msgstr "اطلاعات شرکت"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_name
msgid "Company Name"
msgstr "نام شرکت"

#. module: base_setup
#: model:ir.model,name:base_setup.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Configure Document Layout"
msgstr "پیکربندی طرح اسناد"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Configure company rules to automatically create SO/PO when one of your "
"company sells/buys to another of your company."
msgstr ""
"شرایط شرکت را تنظیم کنید تا به‌طور خودکار هنگام فروش یا خرید یکی از شرکت‌های"
" شما از دیگری، سفارش فروش (SO) یا سفارش خرید (PO) ایجاد شود."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Contacts"
msgstr "مخاطبان"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__report_footer
msgid "Custom Report Footer"
msgstr "پاورقی گزارش سفارشی"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__user_default_rights
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "حق دسترسی‌های پیش‌فرض"

#. module: base_setup
#. odoo-python
#: code:addons/base_setup/models/res_config_settings.py:0
#, python-format
msgid "Default User Template not found."
msgstr "قالب پیش‌فرض کاربر یافت نشد."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Document Layout"
msgstr "طرح‌ سند"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__external_report_layout_id
msgid "Document Template"
msgstr "قالب سند"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Documentation"
msgstr "مستندات"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Edit Layout"
msgstr "ویرایش طرح"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Enable the profiling tool. Profiling may impact performance while being "
"active."
msgstr ""
"ابزار پروفایل را فعال کنید. فعال بودن پروفایل ممکن است بر عملکرد تأثیر "
"بگذارد."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Find free high-resolution images from Unsplash"
msgstr "تصاویر رایگان با وضوح بالا را از Unsplash پیدا کنید."

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "متن پاورقی که در پایین تمام گزارش‌ها نمایش داده می‌شود."

#. module: base_setup
#: model:ir.ui.menu,name:base_setup.menu_config
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "General Settings"
msgstr "تنظیمات عمومی"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Geo Localization"
msgstr "بومی‌سازی جغرافیایی"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_geolocalize
msgid "GeoLocalize"
msgstr "بومی‌سازی جغرافیایی"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "GeoLocalize your partners"
msgstr "شرکای خود را بومی‌سازی جغرافیایی کنید"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_product_images
msgid "Get product pictures using barcode"
msgstr "تصاویر محصول را با استفاده از بارکد دریافت کنید"

#. module: base_setup
#: model:ir.model,name:base_setup.model_ir_http
msgid "HTTP Routing"
msgstr "مسیریابی HTTP"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Import & Export"
msgstr "ورود & خروج"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Integrate with mail client plugins"
msgstr "با پلاگین‌های ایمیل مشتری ادغام کنید."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Integrations"
msgstr "ادغام‌ها"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Inter-Company Transactions"
msgstr "معاملات بین‌شرکتی"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__is_root_company
msgid "Is Root Company"
msgstr "شرکت مادر است"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_auth_ldap
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "LDAP Authentication"
msgstr "احراز هویت LDAP"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Languages"
msgstr "زبان‌ها"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Layout"
msgstr "طرح بندی"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Mail Plugin"
msgstr "افزونه ایمیل"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage API Keys"
msgstr "مدیریت کلید‌های API"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Companies"
msgstr "مدیریت شرکت‌ها"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_account_inter_company_rules
msgid "Manage Inter Company"
msgstr "مدیریت بین‌شرکتی"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Languages"
msgstr "مدیریت زبان‌ها"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Users"
msgstr "مدیریت کاربران"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__group_multi_currency
msgid "Multi-Currencies"
msgstr "چند ارزی"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__active_user_count
msgid "Number of Active Users"
msgstr "تعداد کاربران فعال"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_count
msgid "Number of Companies"
msgstr "تعداد شرکت‌ها"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__language_count
msgid "Number of Languages"
msgstr "تعداد زبان‌ها"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "OAuth Authentication"
msgstr "احراز هویت OAuth"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Odoo"
msgstr "اودو"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "On Apple Store"
msgstr "در Apple Store"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "On Google Play"
msgstr "در Google Play"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_partner_autocomplete
msgid "Partner Autocomplete"
msgstr "تکمیل خودکار شریک"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Performance"
msgstr "عملکرد"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Permissions"
msgstr "مجوزها"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Preview Document"
msgstr "پیش‌نمایش سند"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__profiling_enabled_until
msgid "Profiling enabled until"
msgstr "پروفایل سازی فعال شد تا"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Progressive Web App"
msgstr "برنامه وب پیشرفته"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Protect your forms from spam and abuse."
msgstr "فرم‌های خود را از هرزنامه و سوءاستفاده محافظت کنید."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Protect your forms with CF Turnstile."
msgstr "فرم‌های خود را با CF Turnstile محافظت کنید."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Send SMS"
msgstr "ارسال پیامک"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Send texts to your contacts"
msgstr "برای مخاطبین خود پیامک ارسال کنید"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Set custom access rights for new users"
msgstr "تعیین حق دسترسی سفارشی برای کاربران جدید"

#. module: base_setup
#: model:ir.actions.act_window,name:base_setup.action_general_configuration
msgid "Settings"
msgstr "تنظیمات"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__show_effect
msgid "Show Effect"
msgstr "نمایش تاثیر"

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__company_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"کد کشور ISO در دو حرف.\n"
"شما می‌توانید از این فیلد برای جستجوی سریع استفاده کنید."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"This name will be used for the application when Odoo is installed through "
"the browser."
msgstr ""
"این نام برای برنامه زمانی که Odoo از طریق مرورگر نصب می‌شود، استفاده خواهد "
"شد."

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_web_unsplash
msgid "Unsplash Image Library"
msgstr "کتابخانه تصویر Unsplash"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Update Info"
msgstr "به‌روزرسانی اطلاعات"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use LDAP credentials to log in"
msgstr "برای ورود به سیستم از اعتبارنامه LDAP استفاده کنید"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use external accounts to log in (Google, Facebook, etc.)"
msgstr ""
"از حساب‌های خارجی برای ورود به سیستم استفاده کنید (Google، Facebook و غیره)."

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_auth_oauth
msgid "Use external authentication providers (OAuth)"
msgstr "از ارائه دهندگان احراز هویت خارجی استفاده کنید (OAuth)"

#. module: base_setup
#: model:ir.model,name:base_setup.model_res_users
msgid "User"
msgstr "کاربر"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Users"
msgstr "کاربران"

#. module: base_setup
#. odoo-python
#: code:addons/base_setup/models/res_config_settings.py:0
#, python-format
msgid "VAT"
msgstr "ارزش افزوده"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"When populating your address book, Odoo provides a list of matching "
"companies. When selecting one item, the company data and logo are auto-"
"filled."
msgstr ""
"هنگام پر کردن دفترچه آدرس خود، Odoo فهرستی از شرکت‌های مطابقت‌دهنده را ارائه"
" می‌دهد. با انتخاب یک مورد، داده‌ها و لوگوی شرکت به‌صورت خودکار پر می‌شوند."

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_recaptcha
msgid "reCAPTCHA"
msgstr "reCAPTCHA"
