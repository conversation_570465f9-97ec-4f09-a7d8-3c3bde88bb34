# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_setup
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 12:31+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "<span class=\"fa fa-lg fa-users\" aria-label=\"Number of active users\"/>"
msgstr "<span class=\"fa fa-lg fa-users\" aria-label=\"Number of active users\"/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" invisible=\"active_user_count &gt; 1\">\n"
"                                        Active User\n"
"                                    </span>\n"
"                                    <span class=\"o_form_label\" invisible=\"active_user_count &lt;= 1\">\n"
"                                        Active Users\n"
"                                    </span>"
msgstr ""
"<span class=\"o_form_label\" invisible=\"active_user_count &gt; 1\">\n"
"                                        مستخدم نشط\n"
"                                    </span>\n"
"                                    <span class=\"o_form_label\" invisible=\"active_user_count &lt;= 1\">\n"
"                                        مستخدمين نشطين\n"
"                                    </span>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" invisible=\"company_count &gt; 1\">\n"
"                                        Company\n"
"                                    </span>\n"
"                                    <span class=\"o_form_label\" invisible=\"company_count &lt;= 1\">\n"
"                                        Companies\n"
"                                    </span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"o_form_label\" invisible=\"company_count &gt; 1\">\n"
"                                        شركة\n"
"                                    </span>\n"
"                                    <span class=\"o_form_label\" invisible=\"company_count &lt;= 1\">\n"
"                                        شركات\n"
"                                    </span>\n"
"                                    <br/>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\" invisible=\"language_count &gt; 1\">\n"
"                                            Language\n"
"                                        </span>\n"
"                                        <span class=\"o_form_label\" invisible=\"language_count &lt;= 1\">\n"
"                                            Languages\n"
"                                        </span>"
msgstr ""
"<span class=\"o_form_label\" invisible=\"language_count &gt; 1\">\n"
"                                            لغة\n"
"                                        </span>\n"
"                                        <span class=\"o_form_label\" invisible=\"language_count &lt;= 1\">\n"
"                                            لغات\n"
"                                        </span>"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to choose your Geo "
"Provider."
msgstr "<strong>احفظ</strong> هذه الصفحة وعد إلى هنا لاختيار مزودك الجغرافي. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up Cloudflare "
"turnstile."
msgstr ""
"<strong>احفظ</strong> هذه الصفحة ثم عد مجدداً إلى هنا لإعداد Cloudflare "
"turnstile. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up reCaptcha."
msgstr "<strong>احفظ</strong> هذه الصفحة وعد إلى هنا لضبط reCaptcha. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr "قم <strong>بحفظ</strong> هذه الصفحة ثم عُد إلى هنا لضبط الخاصية. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "API Keys"
msgstr "مفاتيح الواجهة البرمجية للتطبيق "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"API Keys allow your users to access Odoo with external tools when multi-"
"factor authentication is enabled."
msgstr ""
"مفاتيح الواجهة البرمجية تتيح لمستخدميك الوصول إلى أودو باستخدام الأدوات "
"الخارجية عندما تكون خاصية المصادقة متعددة العوامل مفعلة. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "About"
msgstr "حول "

#. module: base_setup
#. odoo-python
#: code:addons/base_setup/controllers/main.py:0
#, python-format
msgid "Access Denied"
msgstr "تم رفض الوصول "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Add Languages"
msgstr "إضافة لغات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Add fun feedback and motivate your employees"
msgstr "أضف ملاحظات لطيفة لتحفيز موظفيك "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_mail_plugin
msgid "Allow integration with the mail plugins"
msgstr "السماح بالتكامل مع المكونات الإضافية للبريد الإلكتروني "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_calendar
msgid "Allow the users to synchronize their calendar  with Google Calendar"
msgstr "السماح للمستخدمين بمزامنة التقويم مع تقويم Google "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_microsoft_calendar
msgid "Allow the users to synchronize their calendar with Outlook Calendar"
msgstr "أتح للمستخدمين مزامنة تقويمهم مع تقويم Outlook "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_import
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Allow users to import data from CSV/XLS/XLSX/ODS files"
msgstr "السماح للمستخدمين باستيراد البيانات من ملفات CSV/XLS/XLSX/ODS"

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__group_multi_currency
msgid "Allows to work in a multi currency environment"
msgstr "يتيح لك العمل في بيئة متعددة العملات "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_voip
msgid "Asterisk (VoIP)"
msgstr "علامة النجمة (VoIP)"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Automatically enrich your contact base with company data"
msgstr "قم بإثراء قاعدة جهات اتصالك بمعلومات الشركة "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Automatically generate counterpart documents for orders/invoices between "
"companies"
msgstr "إنشاء مستندات مقابلة تلقائياً للطلبات/الفواتير بين الشركات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"By default, new users get highest access rights for all installed apps."
msgstr ""
"يحصل المستخدمون الجدد على أعلى صلاحيات للوصول لكافة التطبيقات المثبتة بشكل "
"افتراضي. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Choose the layout of your documents"
msgstr "اختر تخطيط مستنداتك "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_website_cf_turnstile
msgid "Cloudflare Turnstile"
msgstr "Cloudflare Turnstile"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Companies"
msgstr "الشركات"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_id
msgid "Company"
msgstr "الشركة "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_country_code
msgid "Company Country Code"
msgstr "كود شركة الدولة "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_informations
msgid "Company Informations"
msgstr "معلومات الشركة "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_name
msgid "Company Name"
msgstr "اسم الشركة "

#. module: base_setup
#: model:ir.model,name:base_setup.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Configure Document Layout"
msgstr "تهيئة تخطيط المستند "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Configure company rules to automatically create SO/PO when one of your "
"company sells/buys to another of your company."
msgstr ""
"قم بضبط قواعد شركتك لإنشاء أوامر بيع أو شراء تلقائياً عندما يقوم أحد موظفي "
"شركتك بعملية بيع أو شراء لشخص آخر في شركتك. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Contacts"
msgstr "جهات الاتصال"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__report_footer
msgid "Custom Report Footer"
msgstr "تذييل مخصص للتقرير "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__user_default_rights
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "صلاحيات الوصول الافتراضية"

#. module: base_setup
#. odoo-python
#: code:addons/base_setup/models/res_config_settings.py:0
#, python-format
msgid "Default User Template not found."
msgstr "لم يتم العثور على قالب المستخدِم الافتراضي. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Document Layout"
msgstr "مخطط المستندات "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__external_report_layout_id
msgid "Document Template"
msgstr "قالب المستند"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Documentation"
msgstr "التوثيق"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Edit Layout"
msgstr "تحرير المخطط"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"Enable the profiling tool. Profiling may impact performance while being "
"active."
msgstr "تفعيل أداة التحليل. قد يؤثر التحليل على الأداء عندما تكون نشطاً. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Find free high-resolution images from Unsplash"
msgstr "تمكن من إيجاد صور عالية الدقة مجانية على Unsplash "

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "تذييل النص يُعرض في الجزء السفلي لكافة التقارير. "

#. module: base_setup
#: model:ir.ui.menu,name:base_setup.menu_config
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "General Settings"
msgstr "الإعدادات العامة"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Geo Localization"
msgstr "الأقلمة الجغرافية "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_base_geolocalize
msgid "GeoLocalize"
msgstr "الأقلمة الجغرافية "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "GeoLocalize your partners"
msgstr "قم بأقلمة شركائك جغرافياً "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_product_images
msgid "Get product pictures using barcode"
msgstr "احصل على صور للمنتج باستخدام الباركود "

#. module: base_setup
#: model:ir.model,name:base_setup.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Import & Export"
msgstr "استيراد وتصدير"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Integrate with mail client plugins"
msgstr "التكامل مع المكونات الإضافية للبريد الإلكتروني "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Integrations"
msgstr "عمليات الربط "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Inter-Company Transactions"
msgstr "المعاملات بين الشركات "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__is_root_company
msgid "Is Root Company"
msgstr "الشركة الأصل "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_auth_ldap
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "LDAP Authentication"
msgstr "المصادقة من خلال LDAP"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Languages"
msgstr "اللغات"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Layout"
msgstr "مخطط"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Mail Plugin"
msgstr "المكون الإضافي للبريد الإلكتروني "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage API Keys"
msgstr "إدارة مفاتيح الواجهة البرمجية للتطبيق "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Companies"
msgstr "إدارة الشركات "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_account_inter_company_rules
msgid "Manage Inter Company"
msgstr "إدارة ما بين الشركات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Languages"
msgstr "إدارة اللغات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Manage Users"
msgstr "إدارة المستخدمين "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__group_multi_currency
msgid "Multi-Currencies"
msgstr "متعدد العملات "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__active_user_count
msgid "Number of Active Users"
msgstr "عدد المستخدمين النشطين "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__company_count
msgid "Number of Companies"
msgstr "عدد المؤسسات"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__language_count
msgid "Number of Languages"
msgstr "عدد اللغات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "OAuth Authentication"
msgstr "المصادقة من خلال OAuth"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Odoo"
msgstr "أودو"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "On Apple Store"
msgstr "في متجر Apple "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "On Google Play"
msgstr "في متجر Google Play "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_partner_autocomplete
msgid "Partner Autocomplete"
msgstr "الإكمال التلقائي للشريك"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Performance"
msgstr "الأداء"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Permissions"
msgstr "الأذونات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Preview Document"
msgstr "معاينة المستند"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__profiling_enabled_until
msgid "Profiling enabled until"
msgstr "التحليل ممكّن حتى "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Progressive Web App"
msgstr "تطبيق الويب التقدمي "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Protect your forms from spam and abuse."
msgstr "احمِ استماراتك من العشوائيات وإساءة الاستخدام "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Protect your forms with CF Turnstile."
msgstr "احمِ استماراتك باستخدام CF Turnstile. "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Send SMS"
msgstr "إرسال رسالة نصية قصيرة "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Send texts to your contacts"
msgstr "أرسل رسائل لجهات اتصالك "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Set custom access rights for new users"
msgstr "تعيين صلاحيات وصول مخصصة للمستخدمين الجدد "

#. module: base_setup
#: model:ir.actions.act_window,name:base_setup.action_general_configuration
msgid "Settings"
msgstr "الإعدادات"

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__show_effect
msgid "Show Effect"
msgstr "إظهار التأثير"

#. module: base_setup
#: model:ir.model.fields,help:base_setup.field_res_config_settings__company_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"كود الدولة حسب المعيار الدولي أيزو المكون من حرفين.\n"
"يمكنك استخدام هذا الحقل لإجراء بحث سريع."

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"This name will be used for the application when Odoo is installed through "
"the browser."
msgstr "سيتم استخدام هذا الاسم للتطبيق عندما يتم تثبيت أودو من خلال المتصفح. "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_web_unsplash
msgid "Unsplash Image Library"
msgstr "مكتبة صور Unsplash"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Update Info"
msgstr "تحديث المعلومات "

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use LDAP credentials to log in"
msgstr "استخدام بيانات LDAP لتسجيل الدخول"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Use external accounts to log in (Google, Facebook, etc.)"
msgstr "تسجيل الدخول باستخدام حسابات خارجية (Google، Facebook، إلخ.) "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_auth_oauth
msgid "Use external authentication providers (OAuth)"
msgstr "استخدام مزودي مُصادقة خارجيين (OAuth)"

#. module: base_setup
#: model:ir.model,name:base_setup.model_res_users
msgid "User"
msgstr "المستخدم"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid "Users"
msgstr "المستخدمون"

#. module: base_setup
#. odoo-python
#: code:addons/base_setup/models/res_config_settings.py:0
#, python-format
msgid "VAT"
msgstr "ضريبة القيمة المضافة"

#. module: base_setup
#: model_terms:ir.ui.view,arch_db:base_setup.res_config_settings_view_form
msgid ""
"When populating your address book, Odoo provides a list of matching "
"companies. When selecting one item, the company data and logo are auto-"
"filled."
msgstr ""
"عند ملء كتيب عنوانينك، يوفر لك أودو قائمة بالشركات المتطابقة. عند اختيار "
"عنصر واحد، يتم ملء بيانات وشعار الشركة تلقائياً. "

#. module: base_setup
#: model:ir.model.fields,field_description:base_setup.field_res_config_settings__module_google_recaptcha
msgid "reCAPTCHA"
msgstr "reCAPTCHA"
