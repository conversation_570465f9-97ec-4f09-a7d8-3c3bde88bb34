<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.base.setup</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="0"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//form" position="inside">
                    <field name="is_root_company" invisible="1"/>
                    <app data-string="General Settings" string="General Settings" name="general_settings" logo="/base/static/description/settings.png">

                        <div id="invite_users">
                            <block title="Users" name="users_setting_container">
                                <setting id="invite_users_setting">
                                    <widget name='res_config_invite_users'/>
                                </setting>
                                <setting id="active_user_setting">
                                    <span class="fa fa-lg fa-users" aria-label="Number of active users"/>
                                    <field name='active_user_count' class="w-auto ps-3 fw-bold"/>
                                    <span class='o_form_label' invisible="active_user_count &gt; 1">
                                        Active User
                                    </span>
                                    <span class='o_form_label' invisible="active_user_count &lt;= 1">
                                        Active Users
                                    </span>
                                    <a href="https://www.odoo.com/documentation/17.0/applications/general/users.html" title="Documentation" class="o_doc_link" target="_blank"></a>
                                    <br/>
                                    <button name="%(base.action_res_users)d" icon="oi-arrow-right" type="action" string="Manage Users" class="btn-link o_web_settings_access_rights"/>
                                </setting>
                            </block>
                        </div>

                        <div id="languages">
                            <block title="Languages" name="languages_setting_container">
                                <setting id="languages_setting">
                                    <!-- TODO This is not an ideal solution but it looks ok on the interface -->
                                    <div class="w-50">
                                        <field name="language_count" class="w-auto ps-1 fw-bold"/>
                                        <span class='o_form_label' invisible="language_count &gt; 1">
                                            Language
                                        </span>
                                        <span class='o_form_label' invisible="language_count &lt;= 1">
                                            Languages
                                        </span>
                                    </div>
                                    <div class="mt8">
                                        <button name="%(base.action_view_base_language_install)d" icon="oi-arrow-right" type="action" string="Add Languages" class="btn-link"/>
                                    </div>
                                    <div class="mt8" groups="base.group_no_one">
                                        <button name="%(base.res_lang_act_window)d" icon="oi-arrow-right" type="action" string="Manage Languages" class="btn-link"/>
                                    </div>
                                </setting>
                            </block>
                        </div>

                        <div id="companies">
                            <block title="Companies" name="companies_setting_container">
                                <field name="company_id" invisible="1"/>
                                <setting id="company_details_settings">
                                    <field name="company_name" nolabel="1" class="fw-bold"/>
                                    <br/>
                                    <field name="company_informations" class="text-muted" style="width: 90%;"/>
                                    <br/>
                                    <button name="open_company" icon="oi-arrow-right" type="object" string="Update Info" class="btn-link"/>
                                </setting>
                                <setting id="companies_setting">
                                    <field name='company_count' nolabel="1" class="w-auto ps-1 fw-bold"/>
                                    <span class='o_form_label' invisible="company_count &gt; 1">
                                        Company
                                    </span>
                                    <span class='o_form_label' invisible="company_count &lt;= 1">
                                        Companies
                                    </span>
                                    <br/>
                                    <div class="mt8">
                                        <button name="%(base.action_res_company_form)d" icon="oi-arrow-right" type="action" string="Manage Companies" class="btn-link"/>
                                    </div>
                                </setting>
                                <setting id="document_layout_setting" string="Document Layout" help="Choose the layout of your documents" company_dependent="1">
                                    <div class="content-group">
                                        <div class="mt16" groups="base.group_no_one">
                                            <label for="external_report_layout_id" string="Layout" class="col-3 col-lg-3 o_light_label"/>
                                            <field name="external_report_layout_id" domain="[('type','=', 'qweb')]" class="oe_inline"/>
                                        </div>
                                            <button name="%(web.action_base_document_layout_configurator)d" string="Configure Document Layout" type="action" class="oe_link" icon="oi-arrow-right"/>
                                            <br groups="base.group_no_one"/>
                                            <button name="edit_external_header" string="Edit Layout" type="object" class="oe_link" groups="base.group_no_one" icon="oi-arrow-right"/>
                                            <br groups="base.group_no_one"/>
                                            <button name="%(web.action_report_externalpreview)d" string="Preview Document" type="action" class="oe_link" groups="base.group_no_one" icon="oi-arrow-right"/>
                                    </div>
                                </setting>
                                <field name="company_id" invisible="1"/>
                                <setting id="inter_company" string="Inter-Company Transactions" company_dependent="1" help="Automatically generate counterpart documents for orders/invoices between companies" groups="base.group_multi_company" title="Configure company rules to automatically create SO/PO when one of your company sells/buys to another of your company.">
                                    <field name="module_account_inter_company_rules" widget="upgrade_boolean"/>
                                    <div class="content-group" invisible="not module_account_inter_company_rules" id="inter_companies_rules">
                                        <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                    </div>
                                </setting>
                            </block>
                        </div>
                        <div id="emails"/>

                        <div id="contacts_settings">
                            <block title="Contacts" name="contacts_setting_container">
                                <setting id="sms" string="Send SMS" documentation="/applications/marketing/sms_marketing/pricing/pricing_and_faq.html" help="Send texts to your contacts" />
                                <setting help="Automatically enrich your contact base with company data" title="When populating your address book, Odoo provides a list of matching companies. When selecting one item, the company data and logo are auto-filled." id="partner_autocomplete">
                                    <field name="module_partner_autocomplete"/>
                                </setting>
                        </block>
                    </div>

                    <block title="Permissions" id="user_default_rights">
                        <setting string="Default Access Rights" help="Set custom access rights for new users" title="By default, new users get highest access rights for all installed apps." id="access_rights">
                            <field name="user_default_rights"/>
                            <div class="content-group" invisible="not user_default_rights">
                                <div class="mt8">
                                    <button type="object" name="open_default_user" string="Default Access Rights" icon="oi-arrow-right" class="btn-link"/>
                                </div>
                            </div>
                        </setting>
                        <setting string="API Keys" help="API Keys allow your users to access Odoo with external tools when multi-factor authentication is enabled." groups="base.group_system">
                            <button type="action" name="%(base.action_apikeys_admin)d" string="Manage API Keys" icon="oi-arrow-right" class="btn-link"/>
                        </setting>
                        <setting string="Import &amp; Export" help="Allow users to import data from CSV/XLS/XLSX/ODS files" documentation="/applications/general/export_import_data.html" groups="base.group_no_one" id="allow_import">
                            <field name="module_base_import" />
                        </setting>
                        <setting id="feedback_motivate_setting" help="Add fun feedback and motivate your employees" groups="base.group_no_one">
                            <field name="show_effect"/>
                        </setting>
                    </block>

                    <block title="Progressive Web App" id="pwa_settings" groups="base.group_no_one">
                        <setting help="This name will be used for the application when Odoo is installed through the browser.">
                            <field name="web_app_name" placeholder="Odoo"/>
                        </setting>
                    </block>

                        <block title="Integrations" name="integration">
                            <setting string="Mail Plugin" documentation="/applications/productivity/mail_plugins.html" help="Integrate with mail client plugins" id="mail_pluggin_setting">
                                <field name="module_mail_plugin" />
                            </setting>
                            <div id="product_get_pic_setting"/>
                            <setting string="OAuth Authentication" help="Use external accounts to log in (Google, Facebook, etc.)" id="module_auth_oauth">
                                <field name="module_auth_oauth" />
                                <div class="content-group mt16" invisible="not module_auth_oauth" id="msg_module_auth_oauth">
                                    <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                </div>
                            </setting>
                            <setting string="LDAP Authentication" help="Use LDAP credentials to log in" documentation="/applications/general/auth/ldap.html" id="module_auth_ldap">
                                <field name="module_auth_ldap"/>
                                <div class="content-group" invisible="not module_auth_ldap" id="auth_ldap_warning">
                                    <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                </div>
                            </setting>
                            <setting documentation="/applications/websites/website/optimize/unsplash.html" help="Find free high-resolution images from Unsplash" id="unsplash">
                                <field name="module_web_unsplash"/>
                                <div class="content-group" invisible="not module_web_unsplash" id="web_unsplash_warning">
                                    <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up the feature.</div>
                                </div>
                            </setting>
                            <setting string="Geo Localization" help="GeoLocalize your partners" id="base_geolocalize">
                                <field name="module_base_geolocalize"/>
                                <div class="content-group" invisible="not module_base_geolocalize" name="base_geolocalize_warning">
                                    <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to choose your Geo Provider.</div>
                                </div>
                            </setting>
                            <setting help="Protect your forms from spam and abuse." id="recaptcha">
                                <field name="module_google_recaptcha"/>
                                <div class="content-group" invisible="not module_google_recaptcha" id="recaptcha_warning">
                                    <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up reCaptcha.</div>
                                </div>
                            </setting>
                            <setting help="Protect your forms with CF Turnstile." id="cf-turnstile">
                                <field name="module_website_cf_turnstile"/>
                                <div class="content-group" invisible="not module_website_cf_turnstile" id="turnstile_warning">
                                    <div class="mt16 text-warning"><strong>Save</strong> this page and come back here to set up Cloudflare turnstile.</div>
                                </div>
                            </setting>

                        </block>

                        <block title="Performance" groups="base.group_no_one" name="performance">
                            <setting id="profiling_enabled_until" help="Enable the profiling tool. Profiling may impact performance while being active.">
                                <field name="profiling_enabled_until"/>
                            </setting>
                        </block>

                        <widget name='res_config_dev_tool'/>
                        <div id='about'>
                            <block title="About" name="about_setting_container">
                                <setting id='appstore'>
                                    <div class="d-flex">
                                        <div>
                                            <!-- FIXME Those links are defined directly in the template which means that we will have to
                                            update the template code is the link ever changes -->
                                            <a class="d-block mx-auto" href="https://play.google.com/store/apps/details?id=com.odoo.mobile" target="blank">
                                                <img alt="On Google Play" class="d-block mx-auto img img-fluid" src="/base_setup/static/src/img/google_play.png"/>
                                            </a>
                                        </div>
                                        <div>
                                            <a class='d-block mx-auto' href="https://itunes.apple.com/us/app/odoo/id1272543640" target="blank">
                                                <img alt="On Apple Store" class="d-block mx-auto img img-fluid" src="/base_setup/static/src/img/app_store.png"/>
                                            </a>
                                        </div>
                                    </div>
                                </setting>
                                <widget name='res_config_edition'/>
                            </block>
                        </div>
                    </app>
                </xpath>
            </field>
        </record>

        <record id="action_general_configuration" model="ir.actions.act_window">
            <field name="name">Settings</field>
            <field name="res_model">res.config.settings</field>
            <field name="view_mode">form</field>
            <field name="target">inline</field>
            <field name="context">{'module' : 'general_settings', 'bin_size': False}</field>
        </record>

        <menuitem
            id="menu_config"
            name="General Settings"
            parent="base.menu_administration"
            sequence="0"
            action="action_general_configuration"
            groups="base.group_system"/>

</odoo>
