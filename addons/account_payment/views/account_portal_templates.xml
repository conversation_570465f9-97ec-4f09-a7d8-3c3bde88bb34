<odoo>
    <template id="portal_my_invoices_payment" name="Payment on My Invoices" inherit_id="account.portal_my_invoices">
        <xpath expr="//t[@t-call='portal.portal_table']/thead/tr/th[last()]" position="before">
            <th></th>
        </xpath>
        <xpath expr="//t[@t-foreach='invoices']/tr/td[last()]" position="before">
            <td class="text-center">
                <a t-if="invoice._has_to_be_paid()"
                    t-att-href="invoice.get_portal_url(anchor='portal_pay')" title="Pay Now" aria-label="Pay now" class="btn btn-sm btn-primary" role="button">
                    <i class="fa fa-arrow-circle-right"/><span class='d-none d-md-inline'> Pay Now</span>
                </a>
            </td>
        </xpath>
        <xpath expr="//t[@name='invoice_status_posted']/span[1]" position="before">
            <t t-set="tx_sudo" t-value="invoice.get_portal_last_transaction()"/>
        </xpath>
        <xpath expr="//span[@name='invoice_status_waiting_for_payment']" position="before">
            <span t-elif="invoice.payment_state in ('not_paid', 'partial') and tx_sudo.state == 'authorized'"
                    class="badge rounded-pill text-bg-primary">
                <i class="fa fa-fw fa-check"/>
                <span class="d-none d-md-inline"> Authorized</span>
            </span>
            <span t-elif="invoice.payment_state in ('not_paid', 'partial') and tx_sudo.state == 'pending' and tx_sudo.provider_code not in ('none', 'custom')"
                    class="badge rounded-pill text-bg-warning">
                <span class="d-none d-md-inline"> Pending</span>
            </span>
            <span t-elif="invoice.payment_state in ('paid', 'in_payment') and tx_sudo.state == 'done'" class="badge rounded-pill text-bg-success">
                <i class="fa fa-fw fa-check"></i>
                <span class="d-none d-md-inline"> Paid</span>
            </span>
        </xpath>
    </template>

    <template id="portal_invoice_payment" name="Invoice Payment">
        <div class="row">
            <div class="modal fade" id="pay_with" role="dialog">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">Pay with</h3>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div t-if="company_mismatch">
                                <t t-call="payment.company_mismatch_warning"/>
                            </div>
                            <div t-elif="not payment_methods_sudo and not tokens_sudo"
                                 class="alert alert-warning"
                            >
                                <strong>No suitable payment method could be found.</strong><br/>
                                If you believe that it is an error, please contact the website
                                administrator.
                            </div>
                            <t t-else="" t-call="payment.form"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <template id="portal_invoice_page_inherit_payment" name="Payment on My Invoices" inherit_id="account.portal_invoice_page">
        <xpath expr="//t[@t-call='portal.portal_record_sidebar']//h2[1]" position="after">
            <t t-set="pending_txs" t-value="invoice.transaction_ids.filtered(lambda tx: tx.state in ('pending', 'authorized') and tx.provider_code not in ('none', 'custom'))"/>
            <div class="d-grid">
                <div t-if="invoice.payment_state in ('paid', 'in_payment')" class="position-sm-absolute position-lg-relative me-auto me-lg-0 top-0 end-0 rounded text-bg-success fs-6 fw-normal badge" >
                    <i class="fa fa-fw fa-check-circle"/> Paid
                </div>
                <div t-elif="pending_txs" class="position-sm-absolute position-lg-relative me-auto me-lg-0 top-0 end-0 rounded text-bg-info fw-normal fs-6 badge" >
                    <i class="fa fa-fw fa-check-circle"/> Pending
                </div>
            </div>
        </xpath>
        <xpath expr="//t[@t-call='portal.portal_record_sidebar']//div[hasclass('o_download_pdf')]" position="before">
            <a t-if="invoice._has_to_be_paid()"
                href="#"
                class="btn btn-primary d-block mb-2"
                data-bs-toggle="modal"
                data-bs-target="#pay_with">
                <i class="fa fa-fw fa-arrow-circle-right"/> Pay Now
            </a>
        </xpath>
        <xpath expr="//div[@id='invoice_content']//div[hasclass('o_portal_html_view')]" position="before">
            <t t-set="tx" t-value="invoice.get_portal_last_transaction()"/>
            <div t-if="invoice.get_portal_last_transaction() and invoice.amount_total and not success and not error and (invoice.payment_state != 'not_paid' or tx.state in ('pending', 'authorized'))"
                 class="o_account_payment_tx_status"
                 t-att-data-invoice-id="invoice.id">
                <t t-call="payment.transaction_status"/>
            </div>
            <div t-if="invoice._has_to_be_paid()" id="portal_pay">
                <t t-call="account_payment.portal_invoice_payment"/>
            </div>
        </xpath>
    </template>

    <template id="portal_invoice_error" name="Invoice error display: payment errors"
            inherit_id="account.portal_invoice_error">
        <xpath expr="//t[@name='generic']" position="after">
            <t t-if="error == 'pay_invoice_invalid_doc'">
                There was an error processing your payment: invalid invoice.
            </t>
            <t t-elif="error == 'pay_invoice_invalid_token'">
                There was en error processing your payment: invalid credit card ID.
            </t>
            <t t-elif="error == 'pay_invoice_tx_fail'">
                There was an error processing your payment: transaction failed.<br />
                <t t-set="tx_sudo" t-value="invoice.get_portal_last_transaction()"/>
                <t t-if="tx_sudo and tx_sudo.state_message">
                    <t t-out="tx_sudo.state_message"/>
                </t>
            </t>
            <t t-elif="error == 'pay_invoice_tx_token'">
                There was an error processing your payment: issue with credit card ID validation.
            </t>
        </xpath>
    </template>

    <template id="portal_invoice_success" name="Invoice success display: payment success"
            inherit_id="account.portal_invoice_success">
        <xpath expr="//a[hasclass('close')]" position="after">
            <t t-if="success == 'pay_invoice'">
                <t t-set="tx_sudo" t-value="invoice.get_portal_last_transaction()"/>
                <span t-if="tx_sudo.provider_id.sudo().done_msg" t-out="tx_sudo.provider_id.sudo().done_msg"/>
                <div t-if="tx_sudo.provider_id.sudo().pending_msg and tx_sudo.provider_code == 'custom' and invoice.ref">
                    <b>Communication: </b><span t-out='invoice.ref'/>
                </div>
            </t>
            <t t-if="success == 'pay_invoice' and invoice.payment_state in ('paid', 'in_payment')">
                Done, your online payment has been successfully processed. Thank you for your order.
            </t>
        </xpath>
    </template>
</odoo>
