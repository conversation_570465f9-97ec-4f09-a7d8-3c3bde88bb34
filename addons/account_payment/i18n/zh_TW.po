# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment
# 
# Translators:
# <PERSON>, 2025
# Wil <PERSON>do<PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid "<b>Communication: </b>"
msgstr "<b>資料傳輸： </b>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"Pay Now</span>"
msgstr ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"立即支付</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-arrow-circle-right\"/> Pay Now"
msgstr "<i class=\"fa fa-fw fa-arrow-circle-right\"/>立即支付"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Authorized</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> 已獲授權</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Paid</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> 已付</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> 已完成支付"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Pending"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/>等待狀態"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "<span class=\"d-none d-md-inline\"> Pending</span>"
msgstr "<span class=\"d-none d-md-inline\"> 待處理</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "<span class=\"o_stat_text\">Payment Transaction</span>"
msgstr "<span class=\"o_stat_text\">付款交易</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"<strong>No suitable payment method could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website\n"
"                                administrator."
msgstr ""
"<strong>找不到合適的支付方式。</strong><br/>\n"
"                                如果您認為出現錯誤，請聯繫網站管理員。"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment provider configuration."
msgstr ""
"<strong>警告！</strong>這筆付款尚有退款正待處理。\n"
"                        請稍等，讓系統先完成處理退款。\n"
"                        若幾分鐘後，退款仍在處理中，請檢查付款服務商相關設置。"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#, python-format
msgid "A payment transaction with reference %s already exists."
msgstr "使用 %s 的付款交易已存在。"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#, python-format
msgid "A token is required to create a new payment transaction."
msgstr "建立新的支付交易需要一個代碼(token)。"

#. module: account_payment
#: model:onboarding.onboarding.step,button_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Activate Stripe"
msgstr "啟用 Stripe"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr "可退款金額"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__amount_paid
#: model:ir.model.fields,field_description:account_payment.field_account_move__amount_paid
#: model:ir.model.fields,field_description:account_payment.field_account_payment__amount_paid
msgid "Amount paid"
msgstr "已付金額"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr "請問您是否確定要取消授權交易嗎？此操作經確定後無法撤消。"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__authorized_transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "已授權的交易"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Capture Transaction"
msgstr "獲取交易"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Close"
msgstr "關閉"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "程式碼"

#. module: account_payment
#: model:ir.model,name:account_payment.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_date
msgid "Created on"
msgstr "建立於"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__currency_id
msgid "Currency"
msgstr "貨幣"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid ""
"Done, your online payment has been successfully processed. Thank you for "
"your order."
msgstr "已完成，您的線上付款已成功處理完畢。 謝謝您的訂單."

#. module: account_payment
#: model:onboarding.onboarding.step,description:account_payment.onboarding_onboarding_step_payment_provider
msgid "Enable credit & debit card payments supported by Stripe."
msgstr "啟用以 Stripe 支援的信用卡及扣賬卡付款。"

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__full_only
msgid "Full Only"
msgstr "只支援全額"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "生成付款連結"

#. module: account_payment
#: model:ir.actions.act_window,name:account_payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "產生付款網址"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr "有待處理的退款"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__id
msgid "ID"
msgstr "識別碼"

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_method_line__payment_provider_state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""
"在測試模式中，會以測試的付款介面處理一筆模擬付款。\n"
"建議在設置付款服務商時，使用此模式測試。"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.res_config_settings_view_form
msgid "Invoice Online Payment"
msgstr "發票線上付款"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "發票"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoice_ids
#, python-format
msgid "Invoices"
msgstr "發票"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr "無效憑單數量"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_journal
msgid "Journal"
msgstr "日記賬"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_move
msgid "Journal Entry"
msgstr "日記賬記項"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr "允許的最大退款"

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__payment_token_id
msgid ""
"Note that only tokens from providers allowing to capture the amount are "
"available."
msgstr "請注意，只限允許收取金額的服務商提供的代碼才可用。"

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_register__payment_token_id
msgid ""
"Note that tokens from providers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr "請注意，服務商若設為只可授權交易（而非收取金額），其提供的代碼便不可用。"

#. module: account_payment
#: model:onboarding.onboarding.step,step_image_alt:account_payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Online Payments"
msgstr "網上付款新手簡介"

#. module: account_payment
#: model:ir.model,name:account_payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "新手簡介步驟"

#. module: account_payment
#: model:onboarding.onboarding.step,title:account_payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "網上付款"

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__partial
msgid "Partial"
msgstr "部份"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_res_config_settings__pay_invoices_online
msgid "Pay Invoices Online"
msgstr "網上繳付發票"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay Now"
msgstr "立即付款"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay now"
msgstr "立即付款"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Pay with"
msgstr "請選擇支付方式"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "付款"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "付款金額"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_provider__journal_id
msgid "Payment Journal"
msgstr "付款日記帳"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_method
#: model:ir.model,name:account_payment.model_account_payment_method_line
#: model:ir.ui.menu,name:account_payment.payment_method_menu
msgid "Payment Methods"
msgstr "付款方式"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_provider
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_id
msgid "Payment Provider"
msgstr "付款服務商"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_provider_menu
msgid "Payment Providers"
msgstr "付款服務商"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr "付款退款引導"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_token_menu
msgid "Payment Tokens"
msgstr "付款代碼(token)"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_transaction
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__transaction_id
msgid "Payment Transaction"
msgstr "付款交易"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_transaction_menu
msgid "Payment Transactions"
msgstr "付款交易"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment
msgid "Payments"
msgstr "付款"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
#, python-format
msgid "Provider"
msgstr "服務商"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#: code:addons/account_payment/models/account_payment.py:0
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
#, python-format
msgid "Refund"
msgstr "退款"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr "退款金額"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr "退款金額"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "退款"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__refunds_count
msgid "Refunds Count"
msgstr "退款次數"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_register
msgid "Register Payment"
msgstr "登記付款"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_journal_form
msgid "SETUP"
msgstr "設定"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr "儲存的付款密鑰"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr "儲存的付款密鑰"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr "來源付款"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_state
msgid "State"
msgstr "狀態"

#. module: account_payment
#: model:onboarding.onboarding.step,done_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "步驟已完成!"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr "合適的支付密鑰"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
#, python-format
msgid "The access token is invalid."
msgstr "存取權杖(token)無效。"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_refund_wizard.py:0
#, python-format
msgid ""
"The amount to be refunded must be positive and cannot be superior to %s."
msgstr "要退還的金額必須為正數且不能高於 %s。"

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_payment_provider__journal_id
msgid "The journal in which the successful transactions are posted."
msgstr "過賬成功交易的日記賬。"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The payment related to the transaction with reference %(ref)s has been "
"posted: %(link)s"
msgstr "參考編號 %(ref)s 的交易，相關付款已過賬： %(link)s"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
#: code:addons/account_payment/controllers/payment.py:0
#, python-format
msgid "The provided parameters are invalid."
msgstr "提供的參數無效。"

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr "相關退款款項來源支付"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: invalid invoice."
msgstr "處理您的付款時出現一處錯誤：無效應收憑單."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid ""
"There was an error processing your payment: issue with credit card ID "
"validation."
msgstr "處理您的付款時有一個錯誤：信用卡身份驗證問題."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: transaction failed.<br/>"
msgstr "處理您的付款時有一個錯誤：交易失敗.<br/>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was en error processing your payment: invalid credit card ID."
msgstr "您有一個付款的錯誤：無效的信用卡號."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment__transaction_ids
msgid "Transactions"
msgstr "交易"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__support_refund
msgid "Type of Refund Supported"
msgstr "支持的退款類型"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr "使用電子支付方式"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Void Transaction"
msgstr "無效交易"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
#, python-format
msgid ""
"You can't delete a payment method that is linked to a provider in the enabled or test state.\n"
"Linked providers(s): %s"
msgstr ""
"若付款方式連結至已啟用或在測試狀態的服務商，便不可刪除。\n"
"已連結服務商： %s"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot uninstall this module as payments using this payment method "
"already exist."
msgstr "因有使用此付款方式的交易存在，因此無法刪除此模組。"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_journal.py:0
#, python-format
msgid ""
"You must first deactivate a payment provider before deleting its journal.\n"
"Linked providers: %s"
msgstr ""
"必須先停用付款服務商，才可刪除其日記賬。\n"
"已連結服務商： %s"
