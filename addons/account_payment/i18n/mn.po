# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment
#
# Translators:
# hish, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# t<PERSON><PERSON><PERSON><PERSON> tsogtoo <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>sk<PERSON><PERSON> <baskhuu<PERSON><EMAIL>>, 2022
# <PERSON>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:48+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"Language: mn\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid "<b>Communication: </b>"
msgstr "<b>Холбох лавлагаа: </b>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> Pay Now</span>"
msgstr "<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> Одоо төлөх</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-arrow-circle-right\"/> Pay Now"
msgstr "<i class=\"fa fa-fw fa-arrow-circle-right\"/> Одоо төлөх"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Authorized</span>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Paid</span>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> Төлөх"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Pending"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> Хүлээгдэж буй"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "<span class=\"d-none d-md-inline\"> Pending</span>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "<span class=\"o_stat_text\">Payment Transaction</span>"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website administrator."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment provider configuration."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#, python-format
msgid "A payment transaction with reference %s already exists."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#, python-format
msgid "A token is required to create a new payment transaction."
msgstr "Шинэ төлбөрийн гүйлгээг хийхийн тулд токен шаардлагатай."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_onboarding_step
msgid "Activate Stripe"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__amount_paid
#: model:ir.model.fields,field_description:account_payment.field_account_move__amount_paid
#: model:ir.model.fields,field_description:account_payment.field_account_payment__amount_paid
msgid "Amount paid"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Are you sure you want to void the authorized transaction? This action can't be undone."
msgstr "Та хянагдсан гүйлгээг буцаах гэж байгаадаа итгэлтэй байна уу? Энэ үйлдлийг хийсний дараа болиулах боломжгүй."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__authorized_transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Хянагдсан гүйлгээ"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Capture Transaction"
msgstr "Гүйлгээг барьж авах"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Close"
msgstr "Хаах"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "Дансны код"

#. module: account_payment
#: model:ir.model,name:account_payment.model_res_company
msgid "Companies"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_uid
msgid "Created by"
msgstr "Үүсгэсэн этгээд"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_date
msgid "Created on"
msgstr "Үүсгэсэн огноо"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__currency_id
msgid "Currency"
msgstr "Валют"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid "Done, your online payment has been successfully processed. Thank you for your order."
msgstr "Дууслаа, таны онлайн төлбөр амжилттай хийгдлээ. Захиалгаа баталгаажуулсанд тань баярлалаа."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_onboarding_step
msgid "Enable credit &amp; debit card payments supported by Stripe"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Борлуулалтын төлбөр төлөх холбоос үүсгэх"

#. module: account_payment
#: model:ir.actions.act_window,name:account_payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "Төлбөрийн холбоос үүсгэх"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_method_line__payment_provider_state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.res_config_settings_view_form
msgid "Invoice Online Payment"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "Нэхэмжлэл (үүд)"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoice_ids
#, python-format
msgid "Invoices"
msgstr "Нэхэмжлэл"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_journal
msgid "Journal"
msgstr "Журнал"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_move
msgid "Journal Entry"
msgstr "Ажил гүйлгээ"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_uid
msgid "Last Updated by"
msgstr "Сүүлд зассан этгээд"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_date
msgid "Last Updated on"
msgstr "Сүүлд зассан огноо"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__payment_token_id
msgid "Note that only tokens from providers allowing to capture the amount are available."
msgstr ""

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_register__payment_token_id
msgid "Note that tokens from providers set to only authorize transactions (instead of capturing the amount) are not available."
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_onboarding_step
msgid "Online Payments"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_onboarding_step
msgid "Online payments enabled"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_res_config_settings__pay_invoices_online
msgid "Pay Invoices Online"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay Now"
msgstr "Одоо төлөх"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay now"
msgstr "Одоо төлөх"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Pay with"
msgstr "Төлөх"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "Төлбөр"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "Төлбөрийн дүн"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_provider__journal_id
msgid "Payment Journal"
msgstr "Төлбөрийн журнал"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_method
#: model:ir.model,name:account_payment.model_account_payment_method_line
#: model:ir.ui.menu,name:account_payment.payment_method_menu
msgid "Payment Methods"
msgstr "Төлбөрийн аргууд"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_provider
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_id
msgid "Payment Provider"
msgstr ""

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_provider_menu
msgid "Payment Providers"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr ""

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_token_menu
msgid "Payment Tokens"
msgstr "Төлбөрийн Токен"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_transaction
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__transaction_id
msgid "Payment Transaction"
msgstr "Төлбөрийн гүйлгээ"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_transaction_menu
msgid "Payment Transactions"
msgstr "Төлбөрийн гүйлгээнүүд"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment
msgid "Payments"
msgstr "Төлбөрүүд"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
#, python-format
msgid "Provider"
msgstr "Үйлчилгээ үзүүлэгч"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
#, python-format
msgid "Refund"
msgstr "Буцаалт"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "Төлбөрийн Буцаалтууд"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__refunds_count
msgid "Refunds Count"
msgstr ""

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_register
msgid "Register Payment"
msgstr "Төлбөр бүртгэх"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_journal_form
msgid "SETUP"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr "Хадгалагдсан төлбөрийн токен"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_state
msgid "State"
msgstr "Төлөв"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
#, python-format
msgid "The access token is invalid."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_refund_wizard.py:0
#, python-format
msgid "The amount to be refunded must be positive and cannot be superior to %s."
msgstr ""

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_payment_provider__journal_id
msgid "The journal in which the successful transactions are posted."
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
#, python-format
msgid "The payment related to the transaction with reference %(ref)s has been posted: %(link)s"
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
#, python-format
msgid "The provided parameters are invalid."
msgstr ""

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: invalid invoice."
msgstr "Таны төлбөрийг гүйцэлдүүлэхэд алдаа үүслээ: нэхэмжлэл хүчингүй."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: issue with credit card ID validation."
msgstr "Таны төлбөрийг гүйцэлдүүлэхэд алдаа үүслээ: кредит картын ID баталгаажилт асуудалтай."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: transaction failed.<br/>"
msgstr "Таны төлбөрийг гүйцэлдүүлэхэд алдаа үүслээ: гүйлгээ амжилтгүй.<br/>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was en error processing your payment: invalid credit card ID."
msgstr "Таны төлбөрийг гүйцэлдүүлэхэд алдаа үүслээ: хүчингүй кредит карт."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment__transaction_ids
msgid "Transactions"
msgstr "Гүйлгээ"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__support_refund
msgid "Type of Refund Supported"
msgstr ""

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr ""

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Void Transaction"
msgstr "Гүйлгээ буцаалт"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_journal.py:0
#, python-format
msgid ""
"You can't delete a payment method that is linked to a provider in the enabled or test state.\n"
"Linked provider(s): %s"
msgstr ""

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_journal.py:0
#, python-format
msgid ""
"You must first deactivate a payment provider before deleting its journal.\n"
"Linked providers: %s"
msgstr ""
