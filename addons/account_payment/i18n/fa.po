# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# odooers ir, 2023
# <PERSON><PERSON> <y.shad<PERSON><PERSON>@gmail.com>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON>@gmail.com>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON>afa <PERSON>hor<PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid "<b>Communication: </b>"
msgstr "<b>ارتباطات: </b>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"Pay Now</span>"
msgstr ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"پرداخت اکنون</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-arrow-circle-right\"/> Pay Now"
msgstr "<i class=\"fa fa-fw fa-arrow-circle-right\"/> پرداخت اکنون"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Authorized</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> مجاز</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Paid</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> پرداخت شده</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> پرداخت شده"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Pending"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> در انتظار"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "<span class=\"d-none d-md-inline\"> Pending</span>"
msgstr "<span class=\"d-none d-md-inline\"> معوقه</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "<span class=\"o_stat_text\">Payment Transaction</span>"
msgstr "<span class=\"o_stat_text\">تراکنش پرداخت</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"<strong>No suitable payment method could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website\n"
"                                administrator."
msgstr ""
"<strong>نمی‌توان هیچ روش پرداخت مناسبی را یافت.</strong><br/>\n"
"                                اگر معتقدید که این یک خطا است، لطفاً با مدیر وب‌سایت تماس بگیرید."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment provider configuration."
msgstr ""
"<strong>هشدار!</strong> There is a refund pending for this یک بازپرداخت معوقه برای این پرداخت وجود دارد. \n"
"یک دقیقه صبر کنید تا پردازش شود. اگر بازپرداخت هنوز معوقه بود، لطفاً پیکربندی ارائه‌ی دهنده‌ی خدمات خود را بررسی کنید."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#, python-format
msgid "A payment transaction with reference %s already exists."
msgstr "یک تراکنش پرداخت با شماره‌ی ارجاع %s از قبل وجود دارد."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#, python-format
msgid "A token is required to create a new payment transaction."
msgstr "برای ایجاد یک تراکنش پرداخت جدید، یک توکن موردنیاز است."

#. module: account_payment
#: model:onboarding.onboarding.step,button_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Activate Stripe"
msgstr "فعال‌سازی نوار"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr "حساب موجود برای بازپرداخت"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__amount_paid
#: model:ir.model.fields,field_description:account_payment.field_account_move__amount_paid
#: model:ir.model.fields,field_description:account_payment.field_account_payment__amount_paid
msgid "Amount paid"
msgstr "مبلغ پرداخت شده"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"آیا مطمئن هستید که می خواهید تراکنش مجاز را از درجه اعتبار ساقط کنید؟ این "
"عملکرد قابل برگشت نیست."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__authorized_transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "تراکنش‌های احراز شد"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Capture Transaction"
msgstr "ضبط تراکنش"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Close"
msgstr "بستن"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "کد"

#. module: account_payment
#: model:ir.model,name:account_payment.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__currency_id
msgid "Currency"
msgstr "ارز"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid ""
"Done, your online payment has been successfully processed. Thank you for "
"your order."
msgstr ""
"انجام شد، پرداخت آنلاین شما با موفقیت پردازش شد. از سفارش شما متشکریم."

#. module: account_payment
#: model:onboarding.onboarding.step,description:account_payment.onboarding_onboarding_step_payment_provider
msgid "Enable credit & debit card payments supported by Stripe."
msgstr "فعال‌سازی پرداخت اعتباری و پرداخت از طریق کارت اعتباری بوسیله‌ی نوار."

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__full_only
msgid "Full Only"
msgstr "فقط کامل"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "تولید لینک پرداخت فروش"

#. module: account_payment
#: model:ir.actions.act_window,name:account_payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "تولید یک لینک پرداخت"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr "یک بازپرداخت معوقه وجود دارد"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__id
msgid "ID"
msgstr "شناسه"

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_method_line__payment_provider_state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""
"در حالت آزمایشی، یک پرداخت جعلی از طریق رابط پرداخت آزمایشی پردازش می‌شود. \n"
"این حالت هنگام تعیین ارائه‌دهنده‌ی خدمات توصیه می‌شود."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.res_config_settings_view_form
msgid "Invoice Online Payment"
msgstr "پرداخت آنلاین فاکتور"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "فاکتور(ها)"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoice_ids
#, python-format
msgid "Invoices"
msgstr "فاکتورها"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr "تعداد فاکتورها"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_journal
msgid "Journal"
msgstr "روزنامه"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_move
msgid "Journal Entry"
msgstr "سند دفترروزنامه‌"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr "حداکثر بازپرداخت مجاز"

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__payment_token_id
msgid ""
"Note that only tokens from providers allowing to capture the amount are "
"available."
msgstr ""
"توجه داشته باشید که تنها توکن‌های ارائه‌دهندگانی که امکان اطلاع از مبلغ را "
"فراهم می‌کنند، موجود هستند."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_register__payment_token_id
msgid ""
"Note that tokens from providers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr ""
"توجه داشته باشید، توکن‌های ارائه‌دهندگانی که تنها برای صدور مجوز (به جای "
"اطلاع از مبلغ) تراکنش‌ها انتخاب شده‌اند موجود نیستند."

#. module: account_payment
#: model:onboarding.onboarding.step,step_image_alt:account_payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Online Payments"
msgstr "وارد کردن پرداخت‌های آنلاین"

#. module: account_payment
#: model:ir.model,name:account_payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "مراحل معارفه سازمانی"

#. module: account_payment
#: model:onboarding.onboarding.step,title:account_payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "پرداخت‌های آنلاین"

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__partial
msgid "Partial"
msgstr "جزئي"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_res_config_settings__pay_invoices_online
msgid "Pay Invoices Online"
msgstr "پرداخت آنلاین فاکتورها"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay Now"
msgstr "اکنون بپرداز"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay now"
msgstr "اکنون بپرداز"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Pay with"
msgstr "پرداخت با"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "پرداخت"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "مقدار پرداخت"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_provider__journal_id
msgid "Payment Journal"
msgstr "روزنامه پرداخت"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_method
#: model:ir.model,name:account_payment.model_account_payment_method_line
#: model:ir.ui.menu,name:account_payment.payment_method_menu
msgid "Payment Methods"
msgstr "روشهای پرداخت"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_provider
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_id
msgid "Payment Provider"
msgstr "سرویس دهنده پرداخت"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_provider_menu
msgid "Payment Providers"
msgstr "سرویس دهندگان پرداخت"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr "برنامه‌ی بازپرداخت مبلغ"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_token_menu
msgid "Payment Tokens"
msgstr "توکن های پرداخت"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_transaction
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__transaction_id
msgid "Payment Transaction"
msgstr "تراکنش پرداخت"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_transaction_menu
msgid "Payment Transactions"
msgstr "تراکنش های پرداخت"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment
msgid "Payments"
msgstr "پرداخت‌ها"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
#, python-format
msgid "Provider"
msgstr "فراهم‌کننده"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#: code:addons/account_payment/models/account_payment.py:0
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
#, python-format
msgid "Refund"
msgstr "بازپرداخت"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr "مقدار بازپرداخت"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr "مقدار بازپرداخت شده"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "برگشت از فروش ها"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__refunds_count
msgid "Refunds Count"
msgstr "تعداد بازپرداخت ها"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_register
msgid "Register Payment"
msgstr "ثبت پرداخت"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_journal_form
msgid "SETUP"
msgstr "شروع"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr "توکن پرداخت ذخیره شده"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr "توکن پرداخت ذخیره شده"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr "پرداخت اولیه"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_state
msgid "State"
msgstr "استان"

#. module: account_payment
#: model:onboarding.onboarding.step,done_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "گام کامل شد!"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr "توکن پرداخت مناسب"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
#, python-format
msgid "The access token is invalid."
msgstr "توکن دسترسی نامعتبر است."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_refund_wizard.py:0
#, python-format
msgid ""
"The amount to be refunded must be positive and cannot be superior to %s."
msgstr "مبلغی بازپرداخت باید مثبت و بیشتر از %s باشد."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_payment_provider__journal_id
msgid "The journal in which the successful transactions are posted."
msgstr "دفتر روزنامه‌ای که تراکنش‌های موفق در آن ثبت می‌شوند."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The payment related to the transaction with reference %(ref)s has been "
"posted: %(link)s"
msgstr "پرداخت مرتبط با تراکنش %(ref)s ثبت شده است: %(link)s"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
#: code:addons/account_payment/controllers/payment.py:0
#, python-format
msgid "The provided parameters are invalid."
msgstr "پارامترهای ارائه شده نامعتبر هستند."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr "پرداخت اولیه‌ی مربوط به بازپرداخت‌ها"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: invalid invoice."
msgstr "خطایی در حین پردازش پرداخت شما رخ داد: فاکتور نامعتبر."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid ""
"There was an error processing your payment: issue with credit card ID "
"validation."
msgstr ""
"در جریان پردازش پرداخت شما خطایی رخ داده است: صدور با اعتبارسنجی شناسه‌ی "
"کارت اعتباری."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: transaction failed.<br/>"
msgstr "در جریان پرداخت شما خطایی رخ داده است: تراکنش ناموفق. <br/>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was en error processing your payment: invalid credit card ID."
msgstr ""
"در جریان پردازش پرداخت شما خطایی رخ داده است: شناسه‌ی کارت اعتباری نامعتبر  "
"است."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment__transaction_ids
msgid "Transactions"
msgstr "تراکنش ها"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__support_refund
msgid "Type of Refund Supported"
msgstr "نوع بازپرداختی که پشتیبانی می‌شود"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr "استفاده از روش پرداخت الکترونیک"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Void Transaction"
msgstr "تراکنش خالی"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
#, python-format
msgid ""
"You can't delete a payment method that is linked to a provider in the enabled or test state.\n"
"Linked providers(s): %s"
msgstr ""
"شما نمی‌توانید روش پرداختی که با یک ارائه‌دهنده در وضعیت فعال یا آزمایشی در ارتباط است را حذف کنید.\n"
"ارائه‌دهنده‌(های) مرتبط: %s"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot uninstall this module as payments using this payment method "
"already exist."
msgstr ""
"نمی‌توانید این ماژول را نصب کنید زیرا از قبل پرداخت‌هایی وجود دارند که از "
"این روش استفاده می‌کنند."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_journal.py:0
#, python-format
msgid ""
"You must first deactivate a payment provider before deleting its journal.\n"
"Linked providers: %s"
msgstr ""
" پیش از حذف دفتر روزنامه‌ی ارائه‌دهنده‌ی خدمات پرداخت، ابتدا باید آن را غیرفعال کنید.\n"
"ارائه‌دهندگان مرتبط: %s"
