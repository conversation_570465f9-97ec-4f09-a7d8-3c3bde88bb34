# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_payment
# 
# Translators:
# Wil Odoo, 2023
# Sarah <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Sarah Park, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid "<b>Communication: </b>"
msgstr "<b>의사소통 : </b>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"Pay Now</span>"
msgstr ""
"<i class=\"fa fa-arrow-circle-right\"/><span class=\"d-none d-md-inline\"> "
"지불하기</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-arrow-circle-right\"/> Pay Now"
msgstr "<i class=\"fa fa-fw fa-arrow-circle-right\"/> 지불하기"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Authorized</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> 승인 완료</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> Paid</span>"
msgstr ""
"<i class=\"fa fa-fw fa-check\"/>\n"
"                <span class=\"d-none d-md-inline\"> 결제 완료</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> 지불 완료"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_page_inherit_payment
msgid "<i class=\"fa fa-fw fa-check-circle\"/> Pending"
msgstr "<i class=\"fa fa-fw fa-check-circle\"/> 보류"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "<span class=\"d-none d-md-inline\"> Pending</span>"
msgstr "<span class=\"d-none d-md-inline\"> 보류 중</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "<span class=\"o_stat_text\">Payment Transaction</span>"
msgstr "<span class=\"o_stat_text\">결제 거래</span>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid ""
"<strong>No suitable payment method could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website\n"
"                                administrator."
msgstr ""
"<strong>알맞은 결제 옵션을 찾을 수 없습니다.</strong><br/>\n"
"                                오류가 발생한 경우, 웹사이트 관리자에게 문의하시기\n"
"                                바랍니다."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a refund pending for this payment.\n"
"                        Wait a moment for it to be processed. If the refund is still pending in a\n"
"                        few minutes, please check your payment provider configuration."
msgstr ""
"<strong>주의!</strong> 해당 결제에 대한 환불이 보류 중입니다.\n"
"                        처리될 때까지 잠시만 기다리세요. 이후에도 환불이\n"
"                        계속 보류 중인 경우에는 결제대행업체 환경 설정을 확인하시기 바랍니다."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#, python-format
msgid "A payment transaction with reference %s already exists."
msgstr "%s를  참조로 하는 결제 거래가 이미 존재합니다."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#, python-format
msgid "A token is required to create a new payment transaction."
msgstr "새 결제 거래를 생성하려면 토큰이 필요합니다."

#. module: account_payment
#: model:onboarding.onboarding.step,button_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Activate Stripe"
msgstr "Stripe 활성화"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__amount_available_for_refund
msgid "Amount Available For Refund"
msgstr "환불 가능 금액"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__amount_paid
#: model:ir.model.fields,field_description:account_payment.field_account_move__amount_paid
#: model:ir.model.fields,field_description:account_payment.field_account_payment__amount_paid
msgid "Amount paid"
msgstr "결제 금액"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr "승인된 거래를 취소 하시겠습니까? 이 작업은 되돌릴 수 없습니다."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__authorized_transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__authorized_transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "승인된 거래"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Capture Transaction"
msgstr "거래 포착"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Close"
msgstr "닫기"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_method_code
msgid "Code"
msgstr "코드"

#. module: account_payment
#: model:ir.model,name:account_payment.model_res_config_settings
msgid "Config Settings"
msgstr "환경 설정"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_uid
msgid "Created by"
msgstr "작성자"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__create_date
msgid "Created on"
msgstr "작성일자"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__currency_id
msgid "Currency"
msgstr "통화"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__display_name
msgid "Display Name"
msgstr "표시명"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_success
msgid ""
"Done, your online payment has been successfully processed. Thank you for "
"your order."
msgstr "완료. 귀하의 온라인 결제가 성공적으로 처리되었습니다. 주문해 주셔서 감사합니다."

#. module: account_payment
#: model:onboarding.onboarding.step,description:account_payment.onboarding_onboarding_step_payment_provider
msgid "Enable credit & debit card payments supported by Stripe."
msgstr ""
"Stripe가 지원되는 신용카드 및\n"
"직불카드 결제를 활성화합니다."

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__full_only
msgid "Full Only"
msgstr "전체 적용"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "판매 결제 링크 생성"

#. module: account_payment
#: model:ir.actions.act_window,name:account_payment.action_invoice_order_generate_link
msgid "Generate a Payment Link"
msgstr "결제 링크 생성"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__has_pending_refund
msgid "Has a pending refund"
msgstr "보류 중인 환불 건이 있습니다"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_method_line__payment_provider_state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""
"테스트 모드에서는 테스트 결제용 인터페이스를 통해서 결제를 가짜로 처리합니다.\n"
"공급업체 설정 시 이 모드를 사용하시는 것이 좋습니다."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.res_config_settings_view_form
msgid "Invoice Online Payment"
msgstr "청구 대금 온라인 결제"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.payment_transaction_form
msgid "Invoice(s)"
msgstr "청구서"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoice_ids
#, python-format
msgid "Invoices"
msgstr "청구서"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__invoices_count
msgid "Invoices Count"
msgstr "청구서 수"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_journal
msgid "Journal"
msgstr "전표"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_move
msgid "Journal Entry"
msgstr "전표 입력"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_available_for_refund
msgid "Maximum Refund Allowed"
msgstr "최대 환불 가능"

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__payment_token_id
msgid ""
"Note that only tokens from providers allowing to capture the amount are "
"available."
msgstr "금액을 매입할 수 있는 업체의 토큰만 사용하실 수 있습니다."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment_register__payment_token_id
msgid ""
"Note that tokens from providers set to only authorize transactions (instead "
"of capturing the amount) are not available."
msgstr "트랜잭션만 (금액 매입 대신) 승인하도록 설정되어 있는 업체의 토큰은 사용하실 수 없습니다."

#. module: account_payment
#: model:onboarding.onboarding.step,step_image_alt:account_payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Online Payments"
msgstr "온보딩 온라인 결제"

#. module: account_payment
#: model:ir.model,name:account_payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "온보딩 단계"

#. module: account_payment
#: model:onboarding.onboarding.step,title:account_payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "온라인 결제"

#. module: account_payment
#: model:ir.model.fields.selection,name:account_payment.selection__payment_refund_wizard__support_refund__partial
msgid "Partial"
msgstr "부분"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_res_config_settings__pay_invoices_online
msgid "Pay Invoices Online"
msgstr "온라인 청구서 결제하기"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay Now"
msgstr "지금 결제"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_my_invoices_payment
msgid "Pay now"
msgstr "지불하기"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_payment
msgid "Pay with"
msgstr "결제 방법"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_id
#: model:ir.model.fields,field_description:account_payment.field_payment_transaction__payment_id
msgid "Payment"
msgstr "결제"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__payment_amount
msgid "Payment Amount"
msgstr "결제 금액"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_provider__journal_id
msgid "Payment Journal"
msgstr "결제 전표"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_method
#: model:ir.model,name:account_payment.model_account_payment_method_line
#: model:ir.ui.menu,name:account_payment.payment_method_menu
msgid "Payment Methods"
msgstr "지급 방법"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_provider
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_id
msgid "Payment Provider"
msgstr "결제대행업체"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_provider_menu
msgid "Payment Providers"
msgstr "결제대행업체"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_refund_wizard
msgid "Payment Refund Wizard"
msgstr "결제 환불 마법사"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_token_menu
msgid "Payment Tokens"
msgstr "결제 토큰"

#. module: account_payment
#: model:ir.model,name:account_payment.model_payment_transaction
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_transaction_id
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__transaction_id
msgid "Payment Transaction"
msgstr "지불 거래"

#. module: account_payment
#: model:ir.ui.menu,name:account_payment.payment_transaction_menu
msgid "Payment Transactions"
msgstr "결제 거래"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment
msgid "Payments"
msgstr "결제"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
#, python-format
msgid "Provider"
msgstr "공급업체"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment.py:0
#: code:addons/account_payment/models/account_payment.py:0
#: model_terms:ir.ui.view,arch_db:account_payment.payment_refund_wizard_view_form
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
#, python-format
msgid "Refund"
msgstr "환불"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__amount_to_refund
msgid "Refund Amount"
msgstr "환불 금액"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__refunded_amount
msgid "Refunded Amount"
msgstr "환불된 금액"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_payment_form_inherit_payment
msgid "Refunds"
msgstr "환불"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__refunds_count
msgid "Refunds Count"
msgstr "환불 수"

#. module: account_payment
#: model:ir.model,name:account_payment.model_account_payment_register
msgid "Register Payment"
msgstr "결제 항목을 처리합니다."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.view_account_journal_form
msgid "SETUP"
msgstr "설정"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__payment_token_id
msgid "Saved Payment Token"
msgstr "저장된 결제 토큰"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__payment_token_id
msgid "Saved payment token"
msgstr "저장된 결제 토큰"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__source_payment_id
msgid "Source Payment"
msgstr "원본 결제"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment_method_line__payment_provider_state
msgid "State"
msgstr "시/도"

#. module: account_payment
#: model:onboarding.onboarding.step,done_text:account_payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "설정 완료!"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__suitable_payment_token_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__suitable_payment_token_ids
msgid "Suitable Payment Token"
msgstr "적합한 결제 토큰"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
#, python-format
msgid "The access token is invalid."
msgstr "유효하지 않은 액세스 토큰입니다."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/wizards/payment_refund_wizard.py:0
#, python-format
msgid ""
"The amount to be refunded must be positive and cannot be superior to %s."
msgstr "환불 금액은 양수여야 하며 %s보다 클 수 없습니다."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_payment_provider__journal_id
msgid "The journal in which the successful transactions are posted."
msgstr "전표에 트랜잭션을 성공적으로 반영하여 발행하였습니다. "

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The payment related to the transaction with reference %(ref)s has been "
"posted: %(link)s"
msgstr "참조 내역 %(ref)s인 트랜잭션에 대한 결제 항목이 발행되었습니다: %(link)s"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/controllers/payment.py:0
#: code:addons/account_payment/controllers/payment.py:0
#, python-format
msgid "The provided parameters are invalid."
msgstr "유효하지 않은 매개변수입니다."

#. module: account_payment
#: model:ir.model.fields,help:account_payment.field_account_payment__source_payment_id
msgid "The source payment of related refund payments"
msgstr "환불 금액에 대한 원래 금액"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: invalid invoice."
msgstr "결제 처리 중 오류가 발생했습니다: 유효하지 않은 청구서입니다."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid ""
"There was an error processing your payment: issue with credit card ID "
"validation."
msgstr "지불 처리 중 오류 발생: 신용 카드 ID 검증에 문제가 있습니다."

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was an error processing your payment: transaction failed.<br/>"
msgstr "지불 처리 중 오류 발생: 거래가 실패했습니다.<br/>"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.portal_invoice_error
msgid "There was en error processing your payment: invalid credit card ID."
msgstr "지불 처리 중 오류 발생: 신용 카드 ID가 잘못되었습니다."

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_bank_statement_line__transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_move__transaction_ids
#: model:ir.model.fields,field_description:account_payment.field_account_payment__transaction_ids
msgid "Transactions"
msgstr "거래"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_payment_refund_wizard__support_refund
msgid "Type of Refund Supported"
msgstr "지원되는 환불 유형"

#. module: account_payment
#: model:ir.model.fields,field_description:account_payment.field_account_payment__use_electronic_payment_method
#: model:ir.model.fields,field_description:account_payment.field_account_payment_register__use_electronic_payment_method
msgid "Use Electronic Payment Method"
msgstr "전자 결제 방법 사용"

#. module: account_payment
#: model_terms:ir.ui.view,arch_db:account_payment.account_invoice_view_form_inherit_payment
msgid "Void Transaction"
msgstr "금지된 거래"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_payment_method_line.py:0
#, python-format
msgid ""
"You can't delete a payment method that is linked to a provider in the enabled or test state.\n"
"Linked providers(s): %s"
msgstr ""
"활성화되어 있거나 테스트 중인 업체에 연결되어 있는 결제 수단은 삭제할 수 없습니다. \n"
"연결되어 있는 업체: %s"

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot uninstall this module as payments using this payment method "
"already exist."
msgstr "이 모듈은 삭제할 수 없습니다. 이미 해당 결제 방법을 사용하여 진행한 결제 내역이 있습니다."

#. module: account_payment
#. odoo-python
#: code:addons/account_payment/models/account_journal.py:0
#, python-format
msgid ""
"You must first deactivate a payment provider before deleting its journal.\n"
"Linked providers: %s"
msgstr ""
"전표 삭제 전에 먼저 결제대행업체를 비활성화해야 합니다.\n"
"연결되어 있는 업체: %s"
